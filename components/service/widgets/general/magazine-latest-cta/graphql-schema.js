const { promisify } = require('util')
const schemata = require('@clocklimited/schemata')
const createMagazineIssueSchema = require('../../../magazine-issue/graphql-schema')

const createSchema = (serviceLocator) => {
  return schemata({
    name: 'MagazineLatestCTAWidget',
    properties: {
      magazineIssues: {
        type: schemata.Array(createMagazineIssueSchema(serviceLocator)),
        resolve: async (parent, args, context) => {
          const query = {
            instance: context.instanceId
          }

          const top100Mags = await promisify(
            serviceLocator.magazineIssueService.find
          )(
            {
              ...query,
              categories: {
                $in: [
                  '22ff3ba972a7c3982f7e8aaf363ce905',
                  'ff491c2ff1129b5345a73ec452903ca3'
                ]
              }
            },
            { sort: { issueDate: -1 }, limit: 1 }
          )

          if (parent.categories?.length) {
            query.categories = { $in: parent.categories }
          }

          if (parent.featured) {
            query.featured = true
          }

          const mags = await promisify(
            serviceLocator.magazineIssueService.find
          )(query, {
            sort: { issueDate: -1 },
            limit: top100Mags.length ? 2 : 3
          })

          if (top100Mags.length) {
            return [...mags, ...top100Mags]
          }

          return mags
        }
      }
    }
  })
}

module.exports = createSchema
