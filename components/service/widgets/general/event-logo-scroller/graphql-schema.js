const schemata = require('@clocklimited/schemata')
const createGraphqlImageBuilderProperty = require('../../../asset/lib/graphql-image-builder-property')

const createSchema = (serviceLocator) => {
  const imageVariations = [
    {
      name: 'free',
      crop: 'Free',
      context: 'Image',
      size: { width: 640 }
    }
  ]

  return schemata({
    name: 'EventLogoScrollerWidget',
    properties: {
      images: createGraphqlImageBuilderProperty(
        serviceLocator,
        imageVariations,
        'EventLogoScrollerWidget'
      )
    }
  })
}

module.exports = createSchema
