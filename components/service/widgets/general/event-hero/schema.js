const schemata = require('@clocklimited/schemata')
const required = require('@clocklimited/validity-required')
const createBaseWidget = require('../../../widget/schema')
const {
  createEventButtonSchema
} = require('../../../../service/event/lib/schema-creators')
const {
  config: validationConfig,
  createMaxLengthValidator
} = require('../../../../service/event/validation-config')

const createSchema = () => {
  const name = 'EventHeroWidget'
  return schemata({
    name,
    properties: {
      title: {
        type: String,
        validators: [
          required,
          createMaxLengthValidator(validationConfig[name].title, {
            useHtmlConverter: true
          })
        ]
      },
      subtitle: {
        type: String
      },
      description: {
        type: String,
        validators: [
          createMaxLengthValidator(validationConfig[name].description, {
            useHtmlConverter: true
          })
        ]
      },
      buttonGroup: {
        type: schemata.Array(createEventButtonSchema()),
        defaultValue: () => []
      },
      theme: {
        type: String,
        defaultValue: 'light'
      },
      themeColorOverride: {
        type: String
      },
      useMaxHeight: {
        type: Boolean,
        defaultValue: false
      },
      proxyEventId: {
        type: String
      },
      parallax: {
        type: Boolean,
        defaultValue: false
      },
      backgroundVideoYoutubeId: {
        type: String
      },
      useAsHeading: {
        type: Boolean,
        defaultValue: false
      },
      hideEventLogo: {
        type: Boolean,
        defaultValue: false
      },
      align: {
        type: String,
        defaultValue: 'center'
      },
      sponsorId: {
        type: String
      },
      hasBigButton: {
        type: Boolean,
        defaultValue: false
      },
      ...createBaseWidget()
    }
  })
}

module.exports = createSchema
