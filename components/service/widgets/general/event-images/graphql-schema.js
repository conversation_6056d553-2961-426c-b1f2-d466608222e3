const schemata = require('@clocklimited/schemata')
const createGraphqlImageBuilderProperty = require('../../../asset/lib/graphql-image-builder-property')

const createSchema = (serviceLocator) => {
  const imageVariations = [
    {
      name: 'landscape',
      crop: 'Landscape',
      context: 'Image',
      size: { width: 800, height: 450 }
    },
    {
      name: 'portrait',
      crop: 'Portrait',
      context: 'Image',
      size: { width: 640, height: 935 }
    }
  ]

  return schemata({
    name: 'EventImagesWidget',
    properties: {
      images: createGraphqlImageBuilderProperty(
        serviceLocator,
        imageVariations,
        'EventImagesWidget'
      )
    }
  })
}

module.exports = createSchema
