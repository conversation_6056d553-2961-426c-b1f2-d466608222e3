const schemata = require('@clocklimited/schemata')
const createGraphqlImageBuilderProperty = require('../../../asset/lib/graphql-image-builder-property')

const createSchema = (serviceLocator) => {
  const imageVariations = [
    {
      name: 'logo',
      crop: 'Free',
      context: 'Logo',
      size: { width: 640 }
    }
  ]
  return schemata({
    name: 'LogoGridWidget',
    properties: {
      images: createGraphqlImageBuilderProperty(
        serviceLocator,
        imageVariations,
        'LogoGridWidget'
      )
    }
  })
}

module.exports = createSchema
