const schemata = require('@clocklimited/schemata')
const createContextValidator = require('validity-cf-image-context-selection')
const createWidgetAreaSchema = require('../../../widget/widget-area-schema')
const createImageWidgetSchema = require('../../../asset/image-schema')
const createCropValidator = require('../../../../../lib/validators/crop-integrity-validator')
const imageConfig = require('../../../../admin/widgets/general/event-feature-boxes/image-config.json')
const createImageCaptionValidator = require('../../../../../lib/validators/image-caption-validator')
const required = require('@clocklimited/validity-required')

const requiredCrops = imageConfig.crops.map((crop) => crop.name)

const urlValidity = require('validity-url-optional-tlds')()

const urlOrPathValidator = (key, keyDisplayName, object, cb) => {
  if (object[key] && object[key].substr(0, 4) === 'http') {
    return urlValidity(key, keyDisplayName, object, cb)
  }
  return urlPathValidator(key, keyDisplayName, object, cb)
}

const urlPathValidator = (key, keyDisplayName, object, cb) => {
  if (object[key] && object[key].substr(0, 1) !== '/') {
    return cb(null, keyDisplayName + ' should be a valid URL path')
  }
  return cb(null, undefined)
}

const createSchema = () =>
  schemata({
    name: 'EventFeatureBoxesItem',
    properties: {
      images: {
        type: createWidgetAreaSchema(createImageWidgetSchema()),
        defaultValue: () => ({}),
        validators: {
          all: [
            createContextValidator(['FeatureBox']),
            createCropValidator(requiredCrops),
            createImageCaptionValidator()
          ]
        }
      },
      title: {
        type: String,
        validators: [required]
      },
      link: {
        type: String
      },
      externalLink: {
        type: Boolean
      },
      caption: {
        type: String
      },
      alt: {
        type: String
      },
      credits: {
        type: String
      },
      originalUrl: {
        type: String
      },
      showOriginalWhenClicked: {
        type: Boolean
      },
      destination: {
        type: String,
        validators: [urlOrPathValidator]
      },
      opensInNewTab: {
        type: Boolean
      }
    }
  })

module.exports = createSchema
