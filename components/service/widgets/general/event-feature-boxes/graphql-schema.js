const schemata = require('@clocklimited/schemata')
const createImageSchema = require('./image-schema')
const extendSchemata = require('../../../../../api/lib/schemata-extender')
const createGraphqlImageBuilderProperty = require('../../../asset/lib/graphql-image-builder-property')

const createSchema = (serviceLocator) => {
  const createGraphqlImageSchema = () => {
    const typeName = 'EventFeatureBoxesItem'
    const imageVariations = [
      {
        name: 'feature_box_640',
        crop: 'FeatureBox',
        context: 'FeatureBox',
        size: { width: 640 }
      }
    ]

    return schemata({
      name: typeName,
      properties: {
        images: createGraphqlImageBuilderProperty(
          serviceLocator,
          imageVariations,
          typeName
        )
      }
    })
  }

  return schemata({
    name: 'EventFeatureBoxesWidget',
    properties: {
      images: {
        type: schemata.Array(
          extendSchemata(createImageSchema(), createGraphqlImageSchema())
        )
      }
    }
  })
}

module.exports = createSchema
