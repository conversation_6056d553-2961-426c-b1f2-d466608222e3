const schemata = require('@clocklimited/schemata')
const {
  createEventButtonSchema,
  createEventButtonGroupResolver
} = require('../../../event/lib/schema-creators')

const createSchema = (serviceLocator) => {
  return schemata({
    name: 'EventHeaderWidget',
    properties: {
      buttonGroup: {
        type: schemata.Array(createEventButtonSchema()),
        resolve: createEventButtonGroupResolver(serviceLocator)
      }
    }
  })
}

module.exports = createSchema
