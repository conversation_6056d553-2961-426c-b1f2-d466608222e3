const schemata = require('@clocklimited/schemata')
const createBaseWidget = require('../../../widget/schema')
const {
  createEventButtonSchema
} = require('../../../../service/event/lib/schema-creators')

const createSchema = (_serviceLocator) => {
  const name = 'EventHeaderWidget'
  return schemata({
    name,
    properties: {
      ...createBaseWidget(),
      title: {
        type: String
      },
      buttonGroup: {
        type: schemata.Array(createEventButtonSchema()),
        defaultValue: () => []
      }
    }
  })
}

module.exports = createSchema
