const schemata = require('@clocklimited/schemata')
const required = require('@clocklimited/validity-required')
// clock length valditor
const validateLength = require('validity-length')
const validateIfPropertyEquals = require('@clocklimited/validity-validate-if-property-equals')
const { promisify } = require('util')
const hat = require('hat')
const {
  config: validationConfig,
  createMaxLengthValidator
} = require('../validation-config')

const createEventArticleCategorySchema = () => {
  return schemata({
    name: 'EventArticleCategory',
    properties: {
      key: {
        type: String,
        validators: [required],
        defaultValue: () => hat()
      },
      name: {
        type: String,
        validators: [required]
      },
      articleBaseSlug: {
        type: String,
        validators: [required, validateLength(1)]
      }
    }
  })
}

const createEventNavigationCtaSchema = () => {
  return schemata({
    name: 'EventNavigationCtaItem',
    properties: {
      key: {
        type: String,
        validators: [required],
        defaultValue: () => hat()
      },
      label: {
        type: String,
        validators: [required]
      },
      theme: {
        type: String,
        validators: [required]
      },
      actionKey: {
        type: String,
        validators: [required]
      }
    }
  })
}

const createSessionTypeSchema = () => {
  return schemata({
    name: 'SessionType',
    properties: {
      key: {
        type: String,
        validators: [required],
        defaultValue: () => hat()
      },
      name: {
        type: String,
        validators: [required]
      },
      shortName: {
        type: String,
        validators: [required]
      },
      tierColour: {
        type: String
      }
    }
  })
}

const createSpeakerRoleSchema = () => {
  return schemata({
    name: 'SpeakerRole',
    properties: {
      key: {
        type: String,
        validators: [required],
        defaultValue: () => hat()
      },
      name: {
        type: String,
        validators: [required]
      }
    }
  })
}

const createPianoActionSchema = () => {
  return schemata({
    name: 'PianoAction',
    properties: {
      key: {
        type: String,
        validators: [required]
      },
      type: {
        type: String /* link | piano | eventLayout */,
        validators: [required]
      },
      link: {
        type: String,
        validators: [validateIfPropertyEquals('type', 'link', required)]
      },
      labelOverride: {
        type: String
      },
      eventLayoutKey: {
        type: String,
        validators: [validateIfPropertyEquals('type', 'eventLayout', required)]
      },
      eventLayoutSlug: {
        type: String
      },
      baseUrl: {
        type: String
      },
      openInNewTab: {
        type: Boolean,
        defaultValue: () => false
      },
      buttonId: {
        type: String
      },
      buttonClassName: {
        type: String
      },
      preventDelete: {
        type: Boolean,
        defaultValue: () => false
      },
      hidden: {
        type: Boolean,
        defaultValue: () => false
      }
    }
  })
}

const createLocationSchema = () => {
  return schemata({
    name: 'Location',
    properties: {
      key: {
        type: String,
        defaultValue: () => hat(),
        validators: {
          all: [required]
        }
      },
      name: {
        type: String,
        validators: {
          all: [required]
        }
      },
      vimeoId: {
        type: String,
        validators: []
      }
    }
  })
}

const createTrackSchema = () => {
  return schemata({
    name: 'Track',
    properties: {
      key: {
        type: String,
        defaultValue: () => hat(),
        validators: {
          all: [required]
        }
      },
      name: {
        type: String,
        validators: {
          all: [required]
        }
      },
      description: {
        type: String,
        validators: []
      }
    }
  })
}

const createEventButtonSchema = () => {
  const name = 'EventButton'
  return schemata({
    name,
    properties: {
      type: {
        type: String,
        validators: [required]
      },
      variant: {
        type: String,
        validators: [required],
        defaultValue: 'secondary'
      },
      label: {
        type: String,
        validators: [
          required,
          createMaxLengthValidator(validationConfig[name].label)
        ]
      },
      parentId: {
        type: String
      },
      link: {
        type: String,
        validators: [validateIfPropertyEquals('type', 'link', required)]
      },
      openInNewTab: {
        type: Boolean,
        defaultValue: false
      },
      labelOverride: {
        type: String
      },
      action: {
        type: String,
        validators: [validateIfPropertyEquals('type', 'action', required)]
      },
      eventLayoutKey: {
        type: String,
        validators: [validateIfPropertyEquals('type', 'eventLayout', required)]
      },
      eventId: {
        type: String
      },
      eventUmbrellaId: {
        type: String
      },
      eventLayoutSlug: {
        type: String
      },
      buttonId: {
        type: String,
        validators: []
      },
      buttonClassName: {
        type: String,
        validators: []
      },
      hidden: {
        type: Boolean,
        defaultValue: () => false
      }
    }
  })
}

const createEventButtonResolver = (serviceLocator) => async (
  layout,
  button
) => {
  let eventUmbrella
  const event = await promisify(serviceLocator.eventService.read)(
    button.eventId
  )
  if (!event) {
    serviceLocator.logger.error('(createEventButtonResolver) No event found')
  } else {
    eventUmbrella = await promisify(serviceLocator.eventUmbrellaService.read)(
      event.eventUmbrellaId
    )

    if (!eventUmbrella) {
      serviceLocator.logger.error(
        '(createEventButtonResolver) Event found, but no event umbrella found'
      )
    } else {
      const baseUrl =
        eventUmbrella && event && `/events/${eventUmbrella.slug}/${event.slug}`

      serviceLocator.logger.info(
        '(createEventButtonResolver) Base url found: ',
        baseUrl
      )

      button.baseUrl = baseUrl
    }
  }

  switch (button.type) {
    case 'eventLayout':
      if (button.eventId) {
        try {
          layout = await promisify(serviceLocator.eventService.findLayout)(
            button.eventId,
            button.eventLayoutKey
          )
          if (layout.key === 'home') {
            layout.slug = ''
          }
        } catch (error) {
          serviceLocator.logger.error('Error finding layout', error)
          return { ...button, eventLayoutSlug: '#' }
        }
      } else if (button.eventUmbrellaId) {
        try {
          layout = await promisify(
            serviceLocator.eventUmbrellaService.findLayout
          )(button.eventUmbrellaId, button.eventLayoutKey)
        } catch (error) {
          serviceLocator.logger.error('Error finding layout', error)
          return { ...button, eventLayoutSlug: '#' }
        }
      } else {
        serviceLocator.logger.error('No event ID or umbrella ID provided')
        return button
      }
      return {
        ...button,
        eventLayoutSlug: layout.slug
      }
    case 'action': {
      const event = await promisify(serviceLocator.eventService.read)(
        button.eventId
      )
      if (!event) {
        serviceLocator.logger.error('No event found')
        return button
      }
      const action = event.pianoActions.find(
        (action) => action.key === button.action
      )
      if (!action) {
        serviceLocator.logger.error('No action found')
        return button
      }
      if (action.type === 'eventLayout' && action.eventLayoutKey) {
        try {
          layout = await promisify(serviceLocator.eventService.findLayout)(
            button.eventId,
            action.eventLayoutKey
          )
          return {
            ...button,
            ...action,
            eventLayoutSlug: layout.slug
          }
        } catch (error) {
          serviceLocator.logger.error('Error finding layout', error)
          return { ...button, eventLayoutSlug: '#' }
        }
      }

      return {
        ...button,
        ...action
      }
    }
    default:
      return button
  }
}

const createEventButtonGroupResolver = (serviceLocator) => async (parent) => {
  let layout
  if (!parent.buttonGroup) {
    serviceLocator.logger.error('No button group found for ', parent)
    return null
  }
  const resolveButton = createEventButtonResolver(serviceLocator)

  const embellishedButtonGroup = parent.buttonGroup.map(
    async (button) => await resolveButton(layout, button)
  )
  const resolvedButtonGroup = await Promise.all(embellishedButtonGroup)
  return resolvedButtonGroup
}

const createTierSchema = () => {
  return schemata({
    name: 'Tier',
    properties: {
      key: {
        type: String,
        validators: [required],
        defaultValue: () => hat()
      },
      name: {
        type: String,
        validators: [required]
      },
      rank: {
        type: Number,
        validators: [required]
      },
      tierColour: {
        type: String
      }
    }
  })
}

module.exports = {
  createTrackSchema,
  createLocationSchema,
  createEventButtonSchema,
  createEventButtonResolver,
  createEventButtonGroupResolver,
  createTierSchema,
  createPianoActionSchema,
  createSpeakerRoleSchema,
  createSessionTypeSchema,
  createEventArticleCategorySchema,
  createEventNavigationCtaSchema
}
