let count = 0

module.exports = function (suffix) {
  count += 1
  const images = {
    widgets: [
      {
        crops: [
          { name: '1:1', src: '1:1' },
          { name: '4:3', src: '4:3' },
          { name: '16:9', src: '16:9' },
          { name: '21:9', src: '21:9' }
        ],
        caption: 'Caption',
        selectedContexts: ['Logo', 'Cover']
      }
    ]
  }
  return {
    name: 'Event Name',
    slug: 'event-name' + (suffix || count),
    account: '123',
    eventUmbrellaId: '123',
    pianoResourceIds: ['1234567'],
    description: 'Hi',
    previewId: '123',
    city: 'Norwich',
    country: 'UK',
    timezone: 'Europe/London',
    startDate: new Date().toISOString(),
    endDate: new Date().toISOString(),
    state: 'Draft',
    lightLogo: 'binary uri',
    darkLogo: 'binary uri',
    articleBaseSlugs: ['news'],
    showLogoInNavigation: true,
    navigationProxyEventId: '123',
    shareDescription: 'share description',
    shareTitle: 'share title',
    metaDescription: 'meta description',
    metaTitle: 'meta title',
    brandColor: '#000000',
    eventPresetId: '123',
    pianoActions: [],
    tiers: [],
    speakerRoles: [],
    sessionTypes: [],
    articleCategories: [],
    useNewNavigationSidebar: false,
    useNavigationDarkTheme: false,
    navigationCtas: [],
    images
  }
}
