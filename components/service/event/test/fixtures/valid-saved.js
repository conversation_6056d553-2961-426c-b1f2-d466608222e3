const extend = require('lodash.assign')
const validNew = require('./valid-new')

module.exports = function () {
  return extend({}, validNew(), {
    _id: 1,
    tracks: [],
    locations: [],
    tags: [],
    articleBaseSlugs: [],
    showLogoInNavigation: false,
    shareTitle: null,
    shareDescription: null,
    metaTitle: null,
    metaDescription: null,
    startDate: null,
    endDate: null,
    timezoneCorrectStartDate: null,
    timezoneCorrectEndDate: null,
    navigation: [],
    eventPresetId: null,
    navigationCtaLabel: null,
    navigationCtaActionKey: null,
    navigationProxyEventId: null,
    createdDate: new Date().toISOString(),
    buildingName: null,
    pianoActions: [],
    tiers: [],
    speakerRoles: [],
    sessionTypes: [],
    articleCategories: [],
    useNewNavigationSidebar: false,
    useNavigationDarkTheme: false,
    navigationCtas: []
  })
}
