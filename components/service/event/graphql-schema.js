const schemata = require('@clocklimited/schemata')
const extendSchemata = require('../../../api/lib/schemata-extender')
const createGraphqlImageBuilderProperty = require('../asset/lib/graphql-image-builder-property')
const createSchema = require('./schema')
const createArticleSchema = require('../article/graphql-schema')
const createEventVideoSchema = require('../event-video/schema')
const createEventAgendaItemSchema = require('./sub-content-types/agenda-item/schema')
const createEventSpeakerSchema = require('./sub-content-types/speaker/schema')
const createEventSponsorSchema = require('./sub-content-types/sponsor/schema')
const createLayoutSchema = require('./sub-content-types/layout/layout-schema')
const embellishEventWithLogos = require('./lib/embellish')
const createNavItemsSchema = require('./sub-content-types/navigation/navigation-item-schema')
const { promisify } = require('util')
const generateImageVariations = require('../event/lib/image-variation-generator')
const { GraphQLString } = require('graphql')
const moment = require('moment')
const convertRichTextToText = require('../../../lib/convert-richtext-to-text')
const {
  createPianoActionSchema,
  createEventButtonResolver,
  createEventNavigationCtaSchema
} = require('./lib/schema-creators')
const slugg = require('slugg')
const createDarkroomUrlBuilder = require('@clocklimited/darkroom-url-builder')
const required = require('@clocklimited/validity-required')
const validateIfPropertyEquals = require('@clocklimited/validity-validate-if-property-equals')

// Helper function to resolve a piano action
const resolveAction = async (serviceLocator, parent, key) => {
  const action = parent.pianoActions.find((b) => b.key === key)
  if (!action) return null
  action.eventId = parent._id
  const resolveButton = createEventButtonResolver(serviceLocator)
  let layout
  const resolvedButton = await resolveButton(layout, action)
  return resolvedButton
}

const createGraphqlSchema = (serviceLocator) => {
  const schema = createSchema(serviceLocator)
  const imageVariations = [
    // ADD BACK TO IMAGE-CONFIG IF NEEDED
    // ...generateImageVariations('Logo', '1x1', '1:1', [320, 668]),
    // ...generateImageVariations('Logo', '4x3', '4:4', [320, 668]),
    // ...generateImageVariations('Logo', '16x9', '16:9', [320, 668]),
    ...generateImageVariations('Cover', '1x1', '1:1', [
      320,
      668,
      900,
      1336,
      1800
    ]),
    ...generateImageVariations('Cover', '4x3', '4:3', [
      320,
      668,
      900,
      1336,
      1800
    ]),
    ...generateImageVariations('Cover', '16x9', '16:9', [
      320,
      668,
      900,
      1336,
      1800
    ]),
    ...generateImageVariations('Cover', '21x9', '21:9', [
      320,
      668,
      900,
      1336,
      1800
    ])
  ]

  const urlBuilder = createDarkroomUrlBuilder(
    serviceLocator.config.darkroom.url,
    serviceLocator.config.darkroom.salt
  )

  const typeName = 'Event'
  const graphqlSchema = schemata({
    name: typeName,
    properties: {
      description: {
        type: String,
        resolve: (parent) => {
          return convertRichTextToText(parent.description)
        }
      },
      darkLogoUrl: {
        type: String,
        resolve: async (parent, args, context) => {
          // If this event has a navigationProxyEventId, use the proxy event's logo
          if (parent.navigationProxyEventId) {
            try {
              let proxyEvent = await promisify(
                serviceLocator.eventService.read
              )(parent.navigationProxyEventId)
              proxyEvent = embellishEventWithLogos(serviceLocator, proxyEvent)
              if (proxyEvent && proxyEvent._darkLogo) {
                return proxyEvent._darkLogo.url()
              }
            } catch (e) {
              serviceLocator.logger.error(
                'Error fetching proxy event dark logo',
                e
              )
            }
          }

          // Fall back to this event's logo
          if (parent?.darkLogo) {
            return urlBuilder()
              .resource(parent.darkLogo)
              .filename(slugg(parent.name) + '-dark-logo.png')
              ?.url()
          }

          return null
        }
      },
      lightLogoUrl: {
        type: String,
        resolve: async (parent, args, context) => {
          // If this event has a navigationProxyEventId, use the proxy event's logo
          if (parent.navigationProxyEventId) {
            try {
              let proxyEvent = await promisify(
                serviceLocator.eventService.read
              )(parent.navigationProxyEventId)
              proxyEvent = embellishEventWithLogos(serviceLocator, proxyEvent)
              if (proxyEvent && proxyEvent._lightLogo) {
                return proxyEvent._lightLogo.url()
              }
            } catch (e) {
              serviceLocator.logger.error(
                'Error fetching proxy event light logo',
                e
              )
            }
          }

          // Fall back to this event's logo
          if (parent?.lightLogo) {
            return urlBuilder()
              .resource(parent.lightLogo)
              .filename(slugg(parent.name) + '-light-logo.png')
              ?.url()
          }

          return null
        }
      },
      images: createGraphqlImageBuilderProperty(
        serviceLocator,
        imageVariations,
        typeName
      ),
      instances: {
        type: Array
      },
      agenda: {
        type: schemata.Array(createEventAgendaItemSchema),
        resolve: async (parent) => {
          const agenda = await promisify(
            serviceLocator.eventService.findAgenda
          )(parent._id)
          return agenda.sort((a, b) => a.startDate - b.startDate)
        }
      },
      currentAgenda: {
        type: schemata.Array(createEventAgendaItemSchema),
        resolve: async (parent) => {
          const agenda = await promisify(
            serviceLocator.eventService.findAgenda
          )(parent._id)
          const now = new Date()
          const currentAgenda = agenda.filter((item) => {
            const isLive = moment(now).isBetween(item.startDate, item.endDate)
            return isLive
          })
          return currentAgenda
        }
      },
      agendaSnapshot: {
        type: schemata({
          name: 'EventAgendaSnapshot',
          properties: {
            currentAgenda: {
              type: schemata.Array(createEventAgendaItemSchema)
            },
            nextAgenda: {
              type: schemata.Array(createEventAgendaItemSchema)
            },
            info: {
              type: schemata({
                name: 'EventAgendaSnapshotInfo',
                properties: {
                  liveStreamStatus: {
                    type: String
                  },
                  completedAgendaCount: {
                    type: Number
                  }
                }
              })
            }
          }
        }),
        resolve: async (parent) => {
          const agenda = await promisify(
            serviceLocator.eventService.findAgenda
          )(parent._id)
          const now = new Date()
          const currentAgenda = agenda.filter((item) => {
            const isLive = moment(now).isBetween(item.startDate, item.endDate)
            return isLive
          })
          const nextAgenda = agenda.filter((item) => {
            const isUpcoming = moment(now).isBefore(item.startDate)
            return isUpcoming
          })

          /**
           * @typedef {Object} LiveStreamInfo
           * @property {string} liveStreamStatus - The current status of the live stream. Possible values: 'NOT_STARTED', 'LIVE', 'ENDED'.
           * @property {number} completedAgendaCount - The count of agenda items that have been completed.
           */
          const info = {
            liveStreamStatus: 'NOT_STARTED',
            completedAgendaCount: 0
          }

          info.completedAgendaCount = agenda.filter((agendaItem) =>
            moment(now).isAfter(agendaItem.endDate)
          ).length

          if (
            !nextAgenda.length &&
            !currentAgenda.length &&
            info.completedAgendaCount > 0
          )
            info.liveStreamStatus = 'ENDED'
          else if (currentAgenda.length) info.liveStreamStatus = 'IN_PROGRESS'
          else if (nextAgenda.length && info.completedAgendaCount > 0)
            info.liveStreamStatus = 'UP_NEXT'
          else if (nextAgenda.length && info.completedAgendaCount === 0)
            info.liveStreamStatus = 'NOT_STARTED'
          else info.liveStreamStatus = 'ERROR'

          return { currentAgenda, nextAgenda, info }
        }
      },
      speakers: {
        type: schemata.Array(createEventSpeakerSchema),
        resolve: async (parent) => {
          const speakers = await promisify(
            serviceLocator.eventService.findSpeakers
          )(parent._id)
          return speakers
        }
      },
      sponsors: {
        type: schemata.Array(createEventSponsorSchema(serviceLocator)),
        resolve: async (parent) => {
          const sponsors = await promisify(
            serviceLocator.eventService.findSponsors
          )(parent._id)
          return sponsors
        }
      },
      sponsor: {
        type: createEventSponsorSchema(serviceLocator),
        resolveArgs: {
          sponsorSlug: { type: GraphQLString }
        },
        resolve: async (parent, args) => {
          if (!args.sponsorSlug) return null
          const sponsors = await promisify(
            serviceLocator.eventService.findSponsors
          )(parent._id)
          const sponsor = sponsors.find((s) => s.slug === args.sponsorSlug)
          return sponsor
        }
      },
      videos: {
        type: schemata.Array(createEventVideoSchema(serviceLocator)),
        resolve: async (parent) => {
          const query = { eventId: parent._id }
          const videos = await promisify(serviceLocator.eventVideoService.find)(
            query
          )
          return videos
        }
      },
      article: {
        type: createArticleSchema(serviceLocator),
        resolveArgs: {
          slug: { type: GraphQLString }
        },
        resolve: async (parent, args, context) => {
          if (!args.slug) return null
          const query = {
            eventId: parent._id,
            slug: args.slug,
            instance: context.instance._id
          }
          const article = await promisify(
            serviceLocator.articleService.findOne
          )(query)
          return article
        }
      },
      layoutProperties: {
        type: createLayoutSchema(serviceLocator, 'EventLayout'),
        resolveArgs: {
          slug: { type: GraphQLString },
          layoutType: {
            type: GraphQLString /* default | article | sponsorBooth | video */
          }
        },
        resolve: async (parent, args) => {
          if (args.slug === null) return null
          switch (args.layoutType) {
            case 'article': {
              const layout = await promisify(
                serviceLocator.eventService.findLayout
              )(parent._id, `article-${args.slug}`)
              return layout
            }
            default: {
              const layout = await promisify(
                serviceLocator.eventService.findLayoutBySlug
              )(parent._id, args.slug)
              return layout
            }
          }
        }
      },
      navItems: {
        type: schemata.Array(
          createNavItemsSchema({
            name: 'EventNavItems',
            includeSubItems: true
          })
        ),
        resolve: async (parent) => {
          let navigation
          if (parent.navigationProxyEventId) {
            const proxyEvent = await promisify(
              serviceLocator.eventService.read
            )(parent.navigationProxyEventId)
            navigation = proxyEvent.navigation
          } else {
            navigation = parent.navigation
          }
          const embellishedNavigationMap = await createNavigation(
            serviceLocator,
            parent,
            navigation
          )
          return Object.keys(embellishedNavigationMap).map(
            (key) => embellishedNavigationMap[key]
          )
        }
      },
      _fullUrl: {
        type: String,
        resolve: async (parent) => {
          const eventUmbrella = await promisify(
            serviceLocator.eventUmbrellaService.read
          )(parent.eventUmbrellaId)
          const primaryInstance = await promisify(
            serviceLocator.instanceService.read
          )(eventUmbrella.primaryInstance)
          const fullUrl = `https://${primaryInstance.subdomain}/events/${eventUmbrella.slug}/${parent.slug}`
          return fullUrl
        }
      },
      proxyEventHomeUrl: {
        type: String,
        resolve: async (parent) => {
          if (!parent.navigationProxyEventId) return null
          try {
            const proxyEvent = await promisify(
              serviceLocator.eventService.read
            )(parent.navigationProxyEventId)
            if (!proxyEvent) return null

            const eventUmbrella = await promisify(
              serviceLocator.eventUmbrellaService.read
            )(proxyEvent.eventUmbrellaId)

            if (!eventUmbrella) return null

            return `/events/${eventUmbrella.slug}/${proxyEvent.slug}`
          } catch (e) {
            serviceLocator.logger.error('Error fetching proxy event URL', e)
            return null
          }
        }
      },
      _registerButtonClassName: {
        type: String,
        resolve: (parent) => {
          const register = parent.pianoActions.find((b) => b.key === 'register')
          return register ? register.buttonClassName : ''
        }
      },
      // Helper function to resolve a special action by key
      _resolveSpecialAction: {
        type: createPianoActionSchema(),
        args: {
          key: { type: String }
        },
        resolve: async (parent, args) => {
          return resolveAction(serviceLocator, parent, args.key)
        }
      },
      // Special actions object containing all reserved action keys
      specialActions: {
        type: schemata({
          name: 'EventSpecialActions',
          properties: {
            register: { type: createPianoActionSchema() },
            live: { type: createPianoActionSchema() },
            navigation_cta: { type: createPianoActionSchema() },
            navigation_logo: { type: createPianoActionSchema() }
          }
        }),
        resolve: async (parent) => {
          const [
            register,
            live,
            // eslint-disable-next-line camelcase
            navigation_cta,
            // eslint-disable-next-line camelcase
            navigation_logo
          ] = await Promise.all([
            resolveAction(serviceLocator, parent, 'register'),
            resolveAction(serviceLocator, parent, 'live'),
            resolveAction(serviceLocator, parent, 'navigation_cta'),
            resolveAction(serviceLocator, parent, 'navigation_logo')
          ])

          return {
            register,
            live,
            navigation_cta,
            navigation_logo
          }
        }
      },
      // Keep these for backward compatibility
      _registerAction: {
        type: createPianoActionSchema(),
        resolve: async (parent) => {
          return resolveAction(serviceLocator, parent, 'register')
        }
      },
      _liveAction: {
        type: createPianoActionSchema(),
        resolve: async (parent) => {
          return resolveAction(serviceLocator, parent, 'live')
        }
      },
      navigationCtas: {
        type: schemata.Array(
          extendSchemata(
            createEventNavigationCtaSchema(),
            schemata({
              name: 'EventNavigationCtaItem',
              properties: {
                type: {
                  type: String /* link | piano | eventLayout */,
                  validators: [required]
                },
                link: {
                  type: String,
                  validators: [
                    validateIfPropertyEquals('type', 'link', required)
                  ]
                },
                labelOverride: {
                  type: String
                },
                eventLayoutKey: {
                  type: String,
                  validators: [
                    validateIfPropertyEquals('type', 'eventLayout', required)
                  ]
                },
                eventLayoutSlug: {
                  type: String
                },
                baseUrl: {
                  type: String
                },
                openInNewTab: {
                  type: Boolean,
                  defaultValue: () => false
                },
                buttonId: {
                  type: String
                },
                buttonClassName: {
                  type: String
                }
              }
            })
          )
        ),
        resolve: async (parent, _, context) => {
          if (Array.isArray(parent.navigationCtas)) {
            const arr = []
            for (const item of parent.navigationCtas) {
              const pianoAction = parent.pianoActions.find(
                (action) => action.key === item.actionKey
              )

              if (pianoAction) {
                // Create a button object that matches the expected format
                const cta = {
                  ...item,
                  key: pianoAction.key,
                  type: pianoAction.type,
                  buttonId: null,
                  buttonClassName: null,
                  eventLayoutKey: null,
                  eventLayoutSlug: null,
                  baseUrl: null,
                  link: null,
                  openInNewTab: false
                }

                switch (pianoAction.type) {
                  case 'eventLayout':
                    try {
                      const layout = await promisify(
                        serviceLocator.eventService.findLayout
                      )(context.event._id, pianoAction.eventLayoutKey)
                      if (layout) {
                        cta.eventLayoutKey = pianoAction.eventLayoutKey
                        cta.eventLayoutSlug = layout.slug
                      }
                    } catch (error) {
                      serviceLocator.logger.error('Error finding layout', error)
                      cta.eventLayoutSlug = '#'
                    }
                    break
                  case 'piano':
                    cta.buttonId = pianoAction.buttonId
                    cta.buttonClassName = pianoAction.buttonClassName
                    break
                  case 'link':
                    cta.link = pianoAction.link
                    cta.openInNewTab = pianoAction.openInNewTab || false
                    break
                }

                arr.push(cta)
              }
            }

            return arr
          }

          return null
        }
      }
    }
  })
  const extendedSchema = extendSchemata(schema, graphqlSchema)
  extendedSchema.isTypeOf = (item) => item.slug && !item.jobTitle
  return extendedSchema
}

const createNavigation = async (serviceLocator, event, navigation) => {
  const acc = {}
  for (const item of navigation) {
    if (item.type === 'eventSection' && item.eventSectionKey) {
      try {
        const layout = await promisify(serviceLocator.eventService.findLayout)(
          event._id,
          item.eventSectionKey
        )
        item.url = layout.slug ? `/${layout.slug}` : '/'
      } catch (error) {
        serviceLocator.logger.error('Error retrieving layout', error)
        item.url = '#'
      }
    }
    if (!item.parent) {
      acc[item.id] = { ...item, subItems: [] }
    } else {
      if (!acc[item.parent].subItems) {
        acc[item.parent].subItems = []
      }
      acc[item.parent].subItems.push(item)
    }
  }
  return acc
}

module.exports = createGraphqlSchema
