.panel.panel-styled(id='section-configuration')
  .panel-header
    h2 Video

  .panel-content

    .form-row(id='field--videoProvider', data-field='videoProvider')
      label
        span.form-label-text Video Provider
          abbr(title='This field is required') *
        select.control.control--choice.form-field(name='videoProvider')
          option(value='') -- Select Provider --
          option(value='youtube', selected=article.videoProvider === 'youtube') YouTube
          option(value='vimeo', selected=article.videoProvider === 'vimeo') Vimeo
      .js-error

    .form-row(id='field--videoId', data-field='videoId')
      label
        span.form-label-text Video Id
          abbr(title='This field is required') *
        input.control.control--text.form-field(type='text', name='videoId', value=article.videoId, autofocus)
      .js-error

    .form-row.form-row-actions
      button.btn.btn--success.js-generate-thumbnail Generate thumbnail
   