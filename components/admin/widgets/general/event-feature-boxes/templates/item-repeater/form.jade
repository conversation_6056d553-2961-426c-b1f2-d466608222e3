extends ../../../../../widget/templates/form/base-item-repeater

block custom-form
  .form-row(id='field--title', data-field='title')
    label
      span.form-label-text Title
      input.control.control--text.form-field(type='text', name='title', value=data.title)
    .js-error

  .form-row(id='field--destination', data-field='destination')
    label
      span.form-label-text Destination
      input.control.control--text.form-field(type='text', name='destination', value=data.destination)
    .js-error
    .form-row-description.form-copy
      p An optional field where you can add an external or internal link to make your image an anchor 

  .form-row.form-row-boolean(id='field--opensInNewTab', data-field='opensInNewTab')
    label
      span.form-label-text Open in a new tab?
      .form-field
        input.control.control--boolean(type='checkbox', name='opensInNewTab', checked=data.opensInNewTab)
    .js-error

  .panel.panel-styled
    .panel-header
      h2 Image
    .panel-content
      .js-image-area
      .form-row.form-row-actions
        input.btn.btn-success.js-image-add(type='submit', value='Choose Image')
