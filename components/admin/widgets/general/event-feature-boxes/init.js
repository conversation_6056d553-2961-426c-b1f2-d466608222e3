const init = (serviceLocator, done) => {
  const widget = {
    editView: require('./views/form'),
    model: require('./models/model'),
    name: 'Event Feature Boxes',
    itemView: require('./views/item'),
    description: ''
  }

  for (const type of [
    'event',
    'eventUmbrella',
    'eventArticle',
    'eventSponsorBody'
  ]) {
    serviceLocator.widgetFactories
      .get(type)
      .register('eventFeatureBoxes', widget)
  }

  done()
}

module.exports = () => ({
  eventFeatureBoxesWidget: ['widget', 'sectionAdmin', init]
})
