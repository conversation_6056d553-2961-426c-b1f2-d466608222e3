const BaseModel = require('cf-base-model')
const schemata = require('../../../../../service/widgets/general/event-feature-boxes/schema')()
const validateDelegate = require('../../../../../../admin/source/js/lib/validate-delegate')()
const config = window.config
const createImageUrlBuilder = require('cf-image-url-builder')

const type = 'eventFeatureBoxes'

module.exports = BaseModel.extend({
  schemata,
  type,
  defaults() {
    return schemata.makeDefault({ type })
  },
  validate: validateDelegate,
  getPreviewImageUrls() {
    const imagesArray = this.get('images')

    if (imagesArray.length === 0) {
      return []
    }

    return imagesArray.map((image) => {
      const widgetImage = image.images.widgets

      const drUrl = config.darkroom.url
      const drKey = config.darkroom.salt
      const urlBuilder = createImageUrlBuilder(drUrl, drKey, widgetImage)
      const inlineImage = urlBuilder.getImage('FeatureBox')

      if (!inlineImage) {
        return null
      }

      return inlineImage.crop('Landscape').constrain(300).url()
    })
  }
})
