const BaseModel = require('cf-base-model')
const ImageAreaModel = require('../../../../../asset/models/image-area')
const config = window.config
const createImageUrlBuilder = require('cf-image-url-builder')
const schema = require('../../../../../../service/widgets/general/event-feature-boxes/image-schema')()

module.exports = BaseModel.extend({
  schema,

  defaults() {
    return schema.makeDefault()
  },

  initialize() {
    this.images = new ImageAreaModel(this.get('images'))
  },

  getPreviewImageUrl() {
    const images = this.images.widgets.map((widget) => widget.toJSON())
    const drUrl = config.darkroom.url
    const drKey = config.darkroom.salt
    const urlBuilder = createImageUrlBuilder(drUrl, drKey, images)
    const image = urlBuilder.getImage('FeatureBox')
    if (!image) return null
    return image.crop('Landscape').constrain(300).url()
  },

  validate(cb) {
    this.schema.validate(this.attributes, (ignoreErr, errors) =>
      cb(Object.keys(errors).length > 0 ? errors : undefined)
    )
  }
})
