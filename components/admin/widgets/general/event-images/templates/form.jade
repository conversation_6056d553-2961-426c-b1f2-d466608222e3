extends ../../../../widget/templates/form/base

block custom-form
  .form-row.form-row-boolean(id='field--isCarousel', data-field='isCarousel')
    label
      span.form-label-text Display as a carousel?
      .form-field
        input.control.control--boolean(type='checkbox', name='isCarousel', checked=data.isCarousel)
    .js-error

  .panel.panel-styled
    .panel-header
      h2 Image
    .panel-content
      .js-image-area
      .form-row.form-row-actions
        input.btn.btn-success.js-image-add(type='submit', value='Choose Image')
