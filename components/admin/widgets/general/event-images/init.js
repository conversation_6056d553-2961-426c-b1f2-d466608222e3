const init = (serviceLocator, done) => {
  const widget = {
    editView: require('./views/form'),
    model: require('./models/model'),
    name: 'Event Images',
    itemView: require('./views/item'),
    description: ''
  }

  for (const type of [
    'event',
    'eventUmbrella',
    'eventArticle',
    'eventSponsorBody'
  ]) {
    serviceLocator.widgetFactories.get(type).register('eventImages', widget)
  }

  done()
}

module.exports = () => ({
  eventImagesWidget: ['widget', 'sectionAdmin', init]
})
