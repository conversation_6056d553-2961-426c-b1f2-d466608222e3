const BaseModel = require('cf-base-model')
const schemata = require('../../../../../service/widgets/general/event-images/schema')()
const validateDelegate = require('../../../../../../admin/source/js/lib/validate-delegate')()
const config = window.config
const createImageUrlBuilder = require('cf-image-url-builder')
const ImageAreaModel = require('../../../../asset/models/image-area')

const type = 'eventImages'

module.exports = BaseModel.extend({
  schemata,
  type,
  defaults() {
    return schemata.makeDefault({ type })
  },
  initialize() {
    this.images = new ImageAreaModel(this.get('images'))
  },
  getPreviewImageUrl(context) {
    const images = this.images.widgets.map((widget) => widget.toJSON())
    const drUrl = config.darkroom.url
    const drKey = config.darkroom.salt
    const urlBuilder = createImageUrlBuilder(drUrl, drKey, images)
    const image = urlBuilder.getImage(context)

    if (!image) return null

    return image.crop('1:1').constrain(300).url()
  },
  validate: validateDelegate
})
