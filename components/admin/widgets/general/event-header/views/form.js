const join = require('path').join
const compileJade = require('browjadify-compile')
const BaseWidgetView = require('../../../../widget/views/form/base')
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const ButtonItemRepeaterInit = require('../../../../event/views/components/button-item-repeater')
const debug = require('../../../../../../admin/source/js/lib/debug')(
  'Event Details widget view'
)

module.exports = BaseWidgetView.extend({
  template,
  debug,

  initialize() {
    BaseWidgetView.prototype.initialize.apply(this, arguments)

    const buttonItemRepeater = new ButtonItemRepeaterInit(
      this.options.serviceLocator,
      this.model.get('buttonGroup'),
      { buttonActions: ['action 1', 'action 2'] }
    ).itemRepeater
    this.buttonItemRepeater = buttonItemRepeater
    this.$el
      .find('.js-button-item-repeater')
      .append(buttonItemRepeater.render().$el)
  },
  addCustomFormData(formData) {
    return BaseWidgetView.prototype.addCustomFormData.call(
      this,
      Object.assign({}, formData, {
        buttonGroup: this.buttonItemRepeater.getItems()
      })
    )
  },

  renderRichTextEditor() {
    this.$('.js-text-editor').each((index, value) =>
      this.richTextEditorManager.create(value, {
        height: 100,
        startupFocus: true
      })
    )
  },

  render() {
    BaseWidgetView.prototype.render.call(this)
    this.renderRichTextEditor()
  }
})
