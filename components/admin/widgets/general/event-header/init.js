const init = (serviceLocator, done) => {
  const widget = {
    name: 'Event Header',
    model: require('./models/model'),
    editView: require('./views/form'),
    itemView: require('./views/item'),
    description: 'Displays a header widget'
  }
  //
  // serviceLocator.widgetFactories.get('event').register('eventHeader', widget)
  // serviceLocator.widgetFactories
  //   .get('eventUmbrella')
  //   .register('eventHeader', widget)
  // serviceLocator.widgetFactories
  //   .get('eventArticle')
  //   .register('eventHeader', widget)
  // serviceLocator.widgetFactories
  //   .get('eventSponsorBody')
  //   .register('eventHeader', widget)

  for (const type of [
    'event',
    'eventUmbrella',
    'eventArticle',
    'eventSponsorBody'
  ]) {
    serviceLocator.widgetFactories.get(type).register('eventHeader', widget)
  }

  done()
}

module.exports = () => ({
  eventHeaderWidget: ['widget', 'eventAdmin', init]
})
