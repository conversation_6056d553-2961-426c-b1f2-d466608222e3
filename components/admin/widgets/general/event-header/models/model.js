const BaseModel = require('cf-base-model')
const schemata = require('../../../../../service/widgets/general/event-header/schema')()
const validateDelegate = require('../../../../../../admin/source/js/lib/validate-delegate')()

const type = 'eventHeader'

module.exports = BaseModel.extend({
  schemata,
  type,
  defaults() {
    return schemata.makeDefault({ type })
  },
  validate: validateDelegate
})
