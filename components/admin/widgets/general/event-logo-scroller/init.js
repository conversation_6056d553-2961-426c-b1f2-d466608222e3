const init = (serviceLocator, done) => {
  const widget = {
    editView: require('./views/form'),
    model: require('./models/model'),
    name: 'Event Logo Scroller',
    itemView: require('./views/item'),
    description: ''
  }

  for (const type of [
    'event',
    'eventUmbrella',
    'eventArticle',
    'eventSponsorBody'
  ]) {
    serviceLocator.widgetFactories
      .get(type)
      .register('eventLogoScroller', widget)
  }

  done()
}

module.exports = () => ({
  eventLogoScrollerWidget: ['widget', 'sectionAdmin', init]
})
