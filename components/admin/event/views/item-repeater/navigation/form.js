const compileJade = require('browjadify-compile')
const join = require('path')
const BaseItemRepeaterFormView = require('../../../../widget/views/form/base-item-repeater')
const template = compileJade(
  join(__dirname, '../../../templates/item-repeater/navigation/form.jade')
)

class NavigationCTARepeaterFormView extends BaseItemRepeaterFormView {
  constructor(...args) {
    super(...args)
    this.template = template
    this.serviceLocator = args[0]
    this.eventId = this.extractEventId(window.location.pathname)
  }

  extractEventId(url) {
    const matches = url.match(/\/events\/([^/]+)/)
    return matches ? matches[1] : null
  }

  getThemes() {
    return [
      {
        key: 'default',
        value: 'Default colours'
      },
      {
        key: 'awards',
        value: 'Awards colours'
      },
      {
        key: 'event',
        value: 'Event colours'
      }
    ]
  }

  loadPianoActions() {
    this.serviceLocator.eventService.read(this.eventId, (err, event) => {
      if (err) {
        console.error(err)
      }

      let innerHTML = '<option>Select action</option>'
      event.pianoActions.forEach((action) => {
        innerHTML += `<option ${
          action.key === this.model.get('actionKey') ? 'selected' : ''
        } value="${action.key}">${action.key}</option>`
      })

      const $select = this.$el.find('.js-navigation-action-select')
      $select.append(innerHTML)
    })
  }

  render() {
    const data = this.model.toJSON()
    data.themes = this.getThemes()
    this.loadPianoActions()
    this.$el.append(this.template({ data }))
    return this
  }
}

module.exports = NavigationCTARepeaterFormView
