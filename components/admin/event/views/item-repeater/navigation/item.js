const compileJade = require('browjadify-compile')
const join = require('path')
const BaseItemRepeaterItemView = require('../../../../widget/views/item/base-item-repeater')
const template = compileJade(
  join(__dirname, '../../../templates/item-repeater/navigation/item.jade')
)

class NavigationRepeaterItemView extends BaseItemRepeaterItemView {
  constructor(serviceLocator, model, ...args) {
    super(serviceLocator, model, ...args)
    this.template = template
  }

  render() {
    const label = this.model.get('label')
    this.$el.append(
      this.template({
        label
      })
    )
    return this
  }
}

module.exports = NavigationRepeaterItemView
