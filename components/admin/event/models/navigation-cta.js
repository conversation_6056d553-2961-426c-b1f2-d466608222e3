const BaseModel = require('cf-base-model')
const {
  createEventNavigationCtaSchema
} = require('../../../service/event/lib/schema-creators')

const schema = createEventNavigationCtaSchema()

module.exports = BaseModel.extend({
  schema,
  defaults() {
    return schema.makeDefault()
  },
  validate(cb) {
    this.schema.validate(this.schema.cast(this.attributes), function (
      ignoreErr,
      errors
    ) {
      if (!errors) return cb()
      return cb(Object.keys(errors).length > 0 ? errors : undefined)
    })
  }
})
