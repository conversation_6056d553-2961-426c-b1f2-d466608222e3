if allowed('event', 'manageNavigation')
  .panel.panel-styled
    .panel-header.js-toggle-content(style="display:flex;flex-direction:column;", data-content="navigation-information")
      h2 Navigation
      .form-copy
        p Within this section you can manage the navigation links

      .panel-actions(style="margin-left:auto;")
        button.btn.btn--success.js-manage-navigation(type='button') Manage
        a.btn
          span.caret

    .panel-content.js-content.navigation-information-content(style="display:none;")
      .form-row.form-row-boolean(id='field--useNewNavigationSidebar', data-field='useNewNavigationSidebar')
        label
          span.form-label-text Use new navigation sidebar
          .form-field
            input.control.control--boolean(type='checkbox', name='useNewNavigationSidebar', checked=data.useNewNavigationSidebar)
            span ⚠️ DEVELOPMENT FEATURE ⚠️
          .js-error

      .form-row.form-row-boolean(id='field--useNavigationDarkTheme', data-field='useNavigationDarkTheme')
        label
          span.form-label-text Use new navigation dark theme
          .form-field
            input.control.control--boolean(type='checkbox', name='useNavigationDarkTheme', checked=data.useNavigationDarkTheme)
            span If checked the navigation will have a dark background and white navigation items
          .js-error

      .form-row.form-row-boolean(id='field--showLogoInNavigation', data-field='showLogoInNavigation')
        label
          span.form-label-text Navigation Logo
          .form-field
            input.control.control--boolean(type='checkbox', name='showLogoInNavigation', checked=data.showLogoInNavigation)
            span Should the logo replace the portfolio button in the navigation &amp; link to the homepage?
          .js-error

      .form-row(id='field--navigationProxyEventId', data-field='navigationProxyEventId')
        label
          span.form-label-text Navigation Proxy Event
          .js-event-select.form-field
        .js-error

      .js-navigation-cta

      .panel-header
        h3 Legacy will be removed when the new navigation is rolled out

        .form-row(id='field--navigationCtaLabel', data-field='navigationCtaLabel')
          label
            span.form-label-text Navigation CTA Label
            input.control.control--text.form-field(type='text', name='navigationCtaLabel', value=data.navigationCtaLabel)
          .js-error

        .form-row(id='field--navigationCtaActionKey', data-field='navigationCtaActionKey')
          label
            span.form-label-text Navigation CTA Action
            select.control.control--choice.form-field.js-navigation-action-select(name='navigationCtaActionKey')
              option(value='') -- Select an action --
              each action in data.pianoActions
                option(selected=data.navigationCtaActionKey === action.key, value=action.key)= action.key
          .form-row-description.form-copy
            p If left blank no button will be shown in the navigation. During the duration of the event the action will be replaced by a LIVE button.
          .js-error


