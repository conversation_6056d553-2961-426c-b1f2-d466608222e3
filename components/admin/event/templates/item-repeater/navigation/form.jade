extends ../../../../widget/templates/form/base-item-repeater
block custom-form
  .panel.panel-styled
    .panel-header
      h2 Navigation CTAs
    .panel-content
      .form-row(id='field--label', data-field='label')
        label
          span.form-label-text Navigation CTA Label
          input.control.control--text.form-field(type='text', name='label', value=data.label)
        .js-error

      .form-row(id='field--theme', data-field='theme')
        label
          span.form-label-text Theme
          select.control.control--choice.form-field.js-navigation-theme-select(name='theme')
            option(value='') Select theme
            each theme in data.themes
              option(selected=data.theme === theme.key, value=theme.key)= theme.value
        .js-error


      .form-row(id='field--actionKey', data-field='actionKey')
        label
          span.form-label-text Navigation CTA Action
          select.control.control--choice.form-field.js-navigation-action-select(name='actionKey')
        .js-error
