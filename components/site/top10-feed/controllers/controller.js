/**
 * Top 10 Feed Controller
 *
 * This controller creates an RSS feed specifically for Top 10 articles.
 * It retrieves only Top 10 articles from the database based on the current instance and account context,
 * and formats them into a simplified RSS XML format.
 *
 * Local Environment Context:
 * - The feed is accessible at /api/top10
 * - It uses the instance and account objects from the request (populated by middleware)
 * - It queries the articleService and authorService from the serviceLocator
 * - Images are served from the darkroom service (configured in config.json)
 * - The feed is instance-specific (filtered by req.instance._id)
 */

// Dependencies
const { promisify } = require('util') // Converts callback-based methods to promises
const moment = require('moment') // Used for date calculations
const createImageUrlBuilder = require('cf-image-url-builder') // For image URL generation

// Custom utilities
const createInstanceFilteredServiceRetriever = require('../../../service/section/lib/instance-filtered-service-retriever')
const articleFilter = require('../../../service/article/filter')

// Import the plain text getter utility
const { getPlaintext } = require('../../../api/article/lib/plain-text-getter')

const IS_DEV_MODE = process.env.NODE_ENV === 'development'

/**
 * Creates and configures the Top 10 feed controller
 *
 * @param {Object} serviceLocator - Service locator containing all required services
 * @returns {Function} Controller setup function
 */
const createController = (serviceLocator) => {
  // Get logger for debugging
  const logger = serviceLocator.logger || console

  // Creates a method that respects instance and account filtering logic
  const retrieveInstanceFilteredServices = createInstanceFilteredServiceRetriever(
    serviceLocator
  )

  const cachedSections = {}

  async function buildArticleUrl(article, baseUrl) {
    const sectionId = article.sections?.[0]
    if (!sectionId) {
      return null
    }

    const section = await getCachedSection(sectionId)
    if (!section) {
      return null
    }

    const url = new URL(section.fullUrlPath, baseUrl)
    url.pathname = `${url.pathname}/${article.slug}`
    url.protocol = IS_DEV_MODE ? 'http:' : 'https:'

    return url
  }

  async function getCachedSection(sectionId) {
    if (sectionId in cachedSections) {
      return cachedSections[sectionId]
    }

    try {
      const results = await promisify(serviceLocator.sectionService.find)({
        _id: sectionId
      })

      const section = results?.[0] || null
      if (section) {
        cachedSections[sectionId] = section
      }

      return section
    } catch (error) {
      console.error('Failed to fetch section:', error)
      return null
    }
  }

  // Helper function to find the optimal image for an article
  const findOptimalImage = (article) => {
    if (!article.images) return ''

    // Target specifically the 668-wide image
    // First priority: hero_landscape_668 (exactly what we want)
    if (
      article.images.hero_landscape_668?.length > 0 &&
      article.images.hero_landscape_668[0]?.url
    ) {
      return article.images.hero_landscape_668[0].url
    }

    // Second priority: any key with "668" and "landscape"
    for (const key in article.images) {
      if (
        key.includes('668') &&
        key.includes('landscape') &&
        article.images[key]?.length > 0 &&
        article.images[key][0]?.url
      ) {
        return article.images[key][0].url
      }
    }

    // Third priority: any key with "668" (maintaining width)
    for (const key in article.images) {
      if (
        key.includes('668') &&
        article.images[key]?.length > 0 &&
        article.images[key][0]?.url
      ) {
        return article.images[key][0].url
      }
    }

    // Fallback to other images if no 668-wide image is found
    // Fourth priority: any landscape image
    for (const key in article.images) {
      if (
        key.includes('landscape') &&
        article.images[key]?.length > 0 &&
        article.images[key][0]?.url
      ) {
        return article.images[key][0].url
      }
    }

    // Last resort: any image
    for (const key in article.images) {
      if (article.images[key]?.length > 0 && article.images[key][0]?.url) {
        return article.images[key][0].url
      }
    }

    return ''
  }

  // Helper function to process image URL for email compatibility
  const processImageUrl = (url) => {
    if (!url) return ''

    // Always ensure we have a .jpg extension
    if (url.includes('.webp')) {
      url = url.replace('.webp', '.jpg')
    } else if (!url.endsWith('.jpg')) {
      // If URL doesn't already end with .jpg, add it
      url = `${url}.jpg`
    }

    return url
  }

  // Helper function to check if an article is a Top 10 article
  const isTop10Article = (article) => {
    // Check if contentType contains "Top 10"
    if (article.contentType && article.contentType.includes('Top 10')) {
      return true
    }
    
    // Check if headline contains "Top 10"
    if (article.headline && article.headline.includes('Top 10')) {
      return true
    }
    
    return false
  }

  // Helper function to get truncated body text
  const getTruncatedBodyText = (article) => {
    if (!article.body || !article.body.widgets) return ''

    // Get plain text from article body
    let fullText = getPlaintext(article.body.widgets)

    // Remove hyperlinks in square brackets
    fullText = fullText.replace(/\[[^\]]*\]/g, '')

    // If text is empty after cleaning, return empty string
    if (!fullText.trim()) return ''

    // Find the nearest period to the 500-character mark or end of text
    const targetLength = Math.min(500, fullText.length)

    // Look for periods within a reasonable range before and after target
    const lowerBound = Math.max(0, targetLength - 100)
    const upperBound = Math.min(fullText.length, targetLength + 100)

    let bestCutoffIndex = targetLength // Default if no period found
    let minDistance = 100 // Initialize with maximum possible distance

    // Check each character in our search range
    for (let i = lowerBound; i < upperBound; i++) {
      if (fullText[i] === '.') {
        const distance = Math.abs(i - targetLength)
        if (distance < minDistance) {
          minDistance = distance
          bestCutoffIndex = i
        }
      }
    }

    // Include the period in the truncated text
    bestCutoffIndex++

    // Ensure we don't go beyond the text length
    bestCutoffIndex = Math.min(bestCutoffIndex, fullText.length)

    return fullText.substring(0, bestCutoffIndex).trim()
  }

  /**
   * Route: /api/top10
   *
   * Query parameters:
   * - limit: Maximum number of articles to include (default: 10)
   * - days: Number of days to look back for articles (default: 30)
   *
   * Response:
   * - Content-Type: application/rss+xml
   * - RSS 2.0 format XML with <item> entries for each Top 10 article
   */
  serviceLocator.router.get('/api/top10', async (req, res) => {
    try {
      // Log request for debugging
      logger.info(
        `Top 10 feed requested for instance: ${req.instance.name} (${req.instance._id})`
      )

      const config = req.query
      logger.debug('Top 10 feed config:', config)

      // Get article filter specific to this instance/account context
      const { applyArticleFilter } = await retrieveInstanceFilteredServices(
        req.instance._id,
        req.account._id
      )

      // Build the Mongo query for articles published in the past X days (default: 30)
      const days = parseInt(config.days || '30')
      const articleQuery = {
        ...articleFilter.publicQuery(applyArticleFilter({})),
        displayDate: {
          $gte: moment().subtract(days, 'days').toDate()
        },
        // Only find Top 10 articles by content type or headline containing "Top 10"
        $or: [
          { contentType: { $regex: 'Top 10', $options: 'i' } },
          { headline: { $regex: 'Top 10', $options: 'i' } }
        ]
      }

      const articleOptions = {
        sort: { displayDate: -1 },
        limit: parseInt(config.limit || '50')
      }

      logger.debug('Top 10 feed article query:', articleQuery)

      // Set projection to include the full URL path
      const articleProjection = {
        headline: 1,
        sell: 1,
        displayDate: 1,
        __fullUrlPath: 1,
        images: 1,
        author: 1,
        legacyAuthorName: 1,
        featured: 1,
        body: 1,
        contentType: 1,
        slug: 1,
        sections: 1
      }

      // Query the articles from the service
      const articles = await promisify(
        serviceLocator.articleService.findPublic
      )(articleQuery, { ...articleOptions, projection: articleProjection })

      logger.debug(`Found ${articles.length} Top 10 articles for feed`)

      // Query the authors (for populating <author> field)
      const authors = await promisify(serviceLocator.authorService.find)(
        { $or: [{ instances: req.instance._id }, { instances: [] }] },
        { projection: { name: 1 } }
      )

      // Construct the base URL using the same logic as the article service
      const protocol = IS_DEV_MODE ? 'http://' : 'https://'
      const subdomain = req.instance.subdomain
      const baseUrl = protocol + subdomain

      // Initialize string to hold all RSS items
      let rssItems = ''
      let regularItems = []

      // Track the newest featured article for special handling
      let newestFeaturedArticleId = null
      let newestFeaturedDate = null

      // Find the newest featured article
      for (const article of articles) {
        if (article.featured) {
          const articleDate = new Date(article.displayDate).getTime()
          if (!newestFeaturedDate || articleDate > newestFeaturedDate) {
            newestFeaturedDate = articleDate
            newestFeaturedArticleId = article._id.toString()
          }
        }
      }

      // Iterate through each article and build individual <item> blocks
      for (const article of articles) {
        const author = authors.find((a) => a._id === article.author)

        // Properly escape title to handle special characters like &
        const title = (article.headline || 'Untitled')
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        const description = (article.sell || '')
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        const pubDate = new Date(article.displayDate).toUTCString()

        const link = await buildArticleUrl(article, baseUrl)

        // Create guid tag using the link as permalink
        const guidTag = link ? `<guid isPermaLink="true">${link}</guid>` : ''

        // Use the image URL builder like other parts of the codebase
        let imageUrl = ''
        if (article.images && article.images.widgets) {
          const urlBuilder = createImageUrlBuilder(
            serviceLocator.config.darkroom.url,
            serviceLocator.config.darkroom.salt,
            article.images.widgets
          )
          imageUrl = urlBuilder
            .getImage('Hero')
            .crop('Landscape')
            .constrain(668)
            .url()

          // Convert to JPG if needed
          if (imageUrl.includes('.webp')) {
            imageUrl = imageUrl.replace('.webp', '.jpg')
          } else if (!imageUrl.endsWith('.jpg')) {
            imageUrl = `${imageUrl}.jpg`
          }
        } else {
          // Fallback to your existing method
          imageUrl = processImageUrl(findOptimalImage(article))
        }

        // Create image tag if an image was found
        const imageTag = imageUrl
          ? `<enclosure url="${imageUrl}" length="0" type="image/jpg" />`
          : ''

        // Format author name
        const authorNameRaw =
          author?.name || article.legacyAuthorName || 'Unknown'
        const authorName = authorNameRaw
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        // Check if this is the newest featured article
        const isNewestFeatured =
          newestFeaturedArticleId &&
          article._id.toString() === newestFeaturedArticleId

        // Get truncated body text
        const bodyText = getTruncatedBodyText(article)
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        // Add body text tag
        const bodyTextTag = bodyText ? `<content>${bodyText}</content>` : ''

        // Add to regular items with <item><article></article></item> layout like adestra-feed
        regularItems.push(`
          <item>
            <article>
              <title>${title}</title>
              <link>${link}</link>
              <description>${description}</description>
              <pubDate>${pubDate}</pubDate>
              <author>${authorName}</author>
              ${guidTag}
              ${imageTag}
              ${bodyTextTag}
            </article>
          </item>`)
      }

      rssItems += regularItems.join('')

      // Create standard RSS 2.0 feed (no namespaces, simple structure, lowercase tags)
      const rssXml = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0">
<channel>
  <title>${req.instance.name} – Top 10 Articles</title>
  <link>${baseUrl}</link>
  <description>Top 10 lists from ${req.instance.name}</description>
  <language>en-us</language>
  <lastbuilddate>${new Date().toUTCString()}</lastbuilddate>
  <ttl>60</ttl>
  ${rssItems}
</channel>
</rss>`

      // Estimate feed size for logging
      const feedSizeBytes = Buffer.byteLength(rssXml, 'utf8')
      const feedSizeMB = (feedSizeBytes / (1024 * 1024)).toFixed(2)

      // Check if feed is within size limits
      const isWithinSizeLimit = feedSizeBytes < 10 * 1024 * 1024 // 10MB limit

      // Response headers for RSS and caching
      res.setHeader('Content-Type', 'application/rss+xml')
      res.setHeader('Cache-Control', 'public, max-age=600') // Allow caching for 10 minutes

      // Add additional headers to help with debugging
      res.setHeader('X-Feed-Size', `${feedSizeMB}MB`)
      res.setHeader('X-Feed-Articles', articles.length)
      res.setHeader('X-Feed-Generated', new Date().toISOString())

      // Send the feed
      res.send(rssXml.trim())

      // Log detailed information about the feed
      logger.info(
        `Top 10 feed successfully generated with ${articles.length} articles, size: ${feedSizeMB}MB`
      )

      if (!isWithinSizeLimit) {
        logger.warn(
          `Top 10 feed exceeds recommended size limit of 10MB. Current size: ${feedSizeMB}MB`
        )
      }

      // Log the feed URL for easy testing
      logger.info(`Feed URL: ${baseUrl}/api/top10`)
    } catch (err) {
      // Catch unexpected errors and log for debugging
      logger.error('Top 10 feed error:', err)

      // Send more detailed error response
      const errorMessage =
        process.env.NODE_ENV === 'development'
          ? `Error generating Top 10 feed: ${err.message}`
          : 'Internal Server Error'

      res.status(500).send(errorMessage)
    }
  })

  return { router: serviceLocator.router }
}

module.exports = createController
