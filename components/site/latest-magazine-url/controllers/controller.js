const { promisify } = require('util')
const IS_DEV_MODE = process.env.NODE_ENV === 'development'

const buildURL = ({ subdomain }, { slug }) => {
  const protocol = IS_DEV_MODE ? 'http' : 'https'
  return `${protocol}://${subdomain}/magazine/${slug}`
}

const createController = (serviceLocator) => {
  serviceLocator.router.get('/latest-magazine', async (req, res, next) => {
    try {
      const query = { instance: req.instance._id }
      const options = { sort: { issueDate: -1 }, limit: 1 }
      const latestMagazine = await promisify(
        serviceLocator.magazineIssueService.findOne
      )(query, options)

      if (!latestMagazine) {
        return res.sendStatus(404)
      }

      const url = buildURL(req.instance, latestMagazine)

      return res.redirect(302, url)
    } catch (error) {
      next(error)
    }
  })
}

module.exports = createController
