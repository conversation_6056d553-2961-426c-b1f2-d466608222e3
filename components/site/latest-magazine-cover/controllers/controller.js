const { promisify } = require('util')
const path = require('path')
const fs = require('fs')
const createImageUrlBuilder = require('cf-image-url-builder')

const createController = (serviceLocator) => {
  serviceLocator.router.get(
    '/api/latest-magazine-cover',
    async (req, res, next) => {
      const serveFallbackImage = () => {
        const fallbackImagePath = path.join(
          __dirname,
          '../assets/fallback-image.png'
        )

        if (!fs.existsSync(fallbackImagePath)) {
          return res.status(404).json({
            error: 'No magazine found and fallback image not available'
          })
        }

        res.setHeader('Content-Type', 'image/png')
        res.setHeader('Cache-Control', 'public, max-age=3600') // Cache for 1 hour

        const fallbackStream = fs.createReadStream(fallbackImagePath)
        fallbackStream.pipe(res)

        fallbackStream.on('error', (streamError) => {
          serviceLocator.logger.error(
            'Error streaming fallback image:',
            streamError
          )
          if (!res.headersSent) {
            res.status(500).json({ error: 'Failed to serve fallback image' })
          }
        })
      }

      try {
        const query = { instance: req.instance._id }

        /**
         * TOOD - Add category based filter (I regret not defining the key for categories as human readible - i.e. monthly_edition, top100, top200)
         */

        const options = { sort: { issueDate: -1 }, limit: 1 }

        const latestMagazine = await promisify(
          serviceLocator.magazineIssueService.findOne
        )(query, options)

        if (!latestMagazine) {
          return serveFallbackImage()
        }

        const urlBuilder = createImageUrlBuilder(
          serviceLocator.config.darkroom.url,
          serviceLocator.config.darkroom.salt,
          latestMagazine.images.widgets
        )

        const imageUrl = urlBuilder.getImage('Cover').crop().url()
        serviceLocator.logger.info(
          '(latest-magazine-cover) Fetching image from',
          imageUrl
        )

        const response = await fetch(imageUrl)

        if (!response.ok) {
          return serveFallbackImage()
        }

        //  Validating image content type
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.startsWith('image/')) {
          return serveFallbackImage()
        }

        res.setHeader('Content-Type', contentType)
        res.setHeader('Cache-Control', 'public, max-age=3600')

        const reader = response.body.getReader()

        const pump = async () => {
          try {
            while (true) {
              const { done, value } = await reader.read()
              if (done) break
              res.write(value)
            }
            res.end()
          } catch (streamError) {
            serviceLocator.logger.error('Error streaming image:', streamError)
            if (!res.headersSent) {
              // If streaming fails, try to serve fallback image
              return serveFallbackImage()
            }
          }
        }

        // Handle client disconnect
        req.on('close', () => {
          reader.cancel()
        })

        await pump()
      } catch (error) {
        return serveFallbackImage()
      }
    }
  )
}

module.exports = createController
