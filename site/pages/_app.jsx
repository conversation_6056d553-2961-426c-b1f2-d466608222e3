import React from 'react'
import { func, node, object } from 'prop-types'
import NextApp from 'next/app'
import Head from 'next/head'
import { NextSeo } from 'next-seo'
import withError from 'next-with-error'
import createDeduper from 'doorman'
import ErrorPage from './_error'
import * as Sentry from '@sentry/browser'
import { DFPSlotsProvider, DFPManager } from 'react-dfp'
import TagManager from 'react-gtm-module'

import packageJson from '../../package.json'
import staticFileMap from '../static-file-map'
import createMapped from 'versionator/lib/middleware/mapped'

import SiteContainer from '@/component/Structure/SiteContainer'
import RootColorVariables from '@/component/Structure/RootColorVariables'
import { ThemeContextProvider } from '@/component/Context/ThemeContext'

import createConfig from '../site.config'
import createUrlInstancifier from '../../components/site/next/lib/url-instancifier'

import '@/asset/scss/base.scss'
import '@/asset/scss/custom-styles.scss'
import brandToColor from '@/lib/brand-to-color'
import { VersionPathContextProvider } from '@/component/Context/VersionPathContext'

// eslint-disable-next-line
import useFeatureFlagHelper from '@/lib/feature-flag-helper'

/** FEATURE FLAGGING
 * Example use of instance-level feature flagging 👇 - features can be added/removed within services/instance/feature-flags.js
 * import useFeatureFlagHelper from '@/lib/feature-flag-helper'
 * const { isFeatureEnabled } = useFeatureFlagHelper(instance)
 * if(isFeaturedEnabled('pianoAds')) { console.log('piano ads are enabled!') }
 */

Sentry.init({
  // dsn: config.sentryDsn
  dsn: ''
})

const mappedVersion = createMapped(staticFileMap)

class App extends NextApp {
  adRefreshDisabled = false
  adRefreshGlobalTimeout = null
  isPianoAdsEnabled = false

  componentDidCatch(error, errorInfo) {
    Sentry.withScope((scope) => {
      Object.keys(errorInfo).forEach((key) => {
        scope.setExtra(key, errorInfo[key])
      })

      Sentry.captureException(error)
    })

    super.componentDidCatch(error, errorInfo)
  }

  getTargeting(
    section,
    article,
    magazineIssue,
    resource,
    // Instance parameter is not used directly in this method
    // but is passed from multiple places in the codebase
    _instance,
    queryParams,
    router
  ) {
    return {
      section_id: section?.slug,
      page_id: article?._id,
      content_type:
        resource?.contentType || magazineIssue?.__typename || 'page',
      category: resource?.category,
      slug: resource?.slug,
      tags: this.getNames(article?.tags, 'tag'),
      author: article?.author?.name,
      companies: this.getNames(article?.companies, 'name'),
      executives: this.getNames(article?.executives, 'name'),
      partners: this.getNames(article?.partners, 'name'),
      magazine_issue: magazineIssue?.slug || resource?.issueSlug,
      magazine_page: resource?.pageNumber,
      environment:
        process.env.NODE_ENV !== 'production' ? 'development' : 'production',
      ad_key: queryParams.ad_key !== undefined ? queryParams.ad_key : undefined,
      company: resource?.contentType === 'Company Report' && resource?._id,
      path: router.asPath.split('?')[0]
    }
  }

  initialloadAdsWithCxUserSegments() {
    // eslint-disable-next-line
    console.log('[CX DEBUG] Starting initialloadAdsWithCxUserSegments')

    // Loading ads with CX user segments
    const { router } = this.props
    // eslint-disable-next-line
    console.log('[CX DEBUG] Router query:', router.query)

    const {
      graphqlManager,
      google_force_console, // eslint-disable-line camelcase
      ...queryParams
    } = router.query
    // eslint-disable-next-line
    console.log('[CX DEBUG] Query params after destructuring:', queryParams)

    const {
      section,
      article,
      magazineIssue,
      resource,
      instance
    } = this.props.pageProps
    // eslint-disable-next-line
    console.log('[CX DEBUG] Page props:', {
      section: section?.name || section,
      article: article?.headline || article,
      magazineIssue: magazineIssue?.title || magazineIssue,
      resource: resource?.title || resource,
      instance: instance?.name || instance
    })

    // Preparing targeting parameters
    // eslint-disable-next-line
    console.log('[CX DEBUG] Getting targeting parameters...')
    const targeting = this.getTargeting(
      section,
      article,
      magazineIssue,
      resource,
      instance,
      queryParams,
      router
    )
    // eslint-disable-next-line
    console.log('[CX DEBUG] Base targeting parameters:', targeting)

    // eslint-disable-next-line
    console.log('[CX DEBUG] Checking window and user conditions...')
    // eslint-disable-next-line
    console.log(
      '[CX DEBUG] - typeof window !== "undefined":',
      typeof window !== 'undefined'
    )
    // eslint-disable-next-line
    console.log(
      '[CX DEBUG] - pageProps.pageData:',
      this.props.pageProps?.pageData
    )
    // eslint-disable-next-line
    console.log(
      '[CX DEBUG] - pageProps.pageData.user:',
      this.props.pageProps?.pageData?.user
    )

    if (typeof window !== 'undefined' && this.props.pageProps?.pageData?.user) {
      // eslint-disable-next-line
      console.log(
        '[CX DEBUG] Window and user conditions met, proceeding with CX logic'
      )

      // Get any test CX parameters from URL
      // eslint-disable-next-line
      console.log('[CX DEBUG] Checking for test CX parameters...')
      const testCxParams = Object.entries(queryParams)
        .filter(([key]) => key.startsWith('tpcx_'))
        .reduce(
          (acc, [key, value]) => ({
            ...acc,
            [key.replace('tpcx_', '')]: value
          }),
          {}
        )
      // eslint-disable-next-line
      console.log('[CX DEBUG] Test CX parameters found:', testCxParams)

      // If we have test parameters, use those instead of actual CX integration
      if (Object.keys(testCxParams).length > 0) {
        // eslint-disable-next-line
        console.log('[CX DEBUG] Using test CX parameters for ad targeting')
        const finalTargeting = {
          ...targeting,
          ...testCxParams
        }
        // eslint-disable-next-line
        console.log(
          '[CX DEBUG] Final targeting with test params:',
          finalTargeting
        )
        return this.DFPManagerLoadAds(finalTargeting)
      }

      // eslint-disable-next-line
      console.log(
        '[CX DEBUG] No test parameters, proceeding with actual CX integration'
      )

      var cX = window.cX || { options: { consent: true } }
      // eslint-disable-next-line
      console.log('[CX DEBUG] CX object initialized:', cX)

      cX.callQueue = cX.callQueue || []
      // eslint-disable-next-line
      console.log('[CX DEBUG] CX callQueue length:', cX.callQueue.length)

      // Initialize CX object for DMP integration
      // eslint-disable-next-line
      console.log('[CX DEBUG] Adding invoke callback to CX callQueue')
      cX.callQueue.push([
        'invoke',
        () => {
          // eslint-disable-next-line
          console.log('[CX DEBUG] CX invoke callback executed')
          // Invoking CX DMP integration
          // Check user data for DMP integration
          // eslint-disable-next-line
          console.log(
            '[CX DEBUG] User sub ID:',
            this.props.pageProps.pageData.user.sub
          )

          // Check DMP site groups for integration
          // eslint-disable-next-line
          console.log(
            '[CX DEBUG] DMP site groups:',
            this.props.pageProps.dmpSiteGroups
          )
          // eslint-disable-next-line
          console.log(
            '[CX DEBUG] DMP site groups results:',
            this.props.pageProps.dmpSiteGroups?.results
          )

          const lookupPayload = {
            identities: [
              { id: this.props.pageProps.pageData.user.sub, type: 'ttb' }
            ],
            siteGroupIds: this.props.pageProps.dmpSiteGroups.results,
            shortIds: true
          }
          // eslint-disable-next-line
          console.log('[CX DEBUG] Lookup payload:', lookupPayload)

          const userSegmentLookupApiUrl =
            'https://api.cxense.com/segment/lookup' +
            '?callback={{callback}}' +
            '&persisted=' +
            encodeURIComponent('8096099a6903ddfa0634039ca9a4e54955f1f094') +
            '&json=' +
            encodeURIComponent(cX.JSON.stringify(lookupPayload))

          // eslint-disable-next-line
          console.log(
            '[CX DEBUG] User segment lookup API URL:',
            userSegmentLookupApiUrl
          )

          // eslint-disable-next-line
          console.log('[CX DEBUG] Making JSONP request to CX API...')
          window.cX.jsonpRequest(userSegmentLookupApiUrl, async (data) => {
            // eslint-disable-next-line
            console.log('[CX DEBUG] JSONP response received:', data)
            // eslint-disable-next-line
            console.log('[CX DEBUG] Response HTTP status:', data.httpStatus)

            if (data.httpStatus && data.httpStatus === 200) {
              // eslint-disable-next-line
              console.log(
                '[CX DEBUG] Successful CX API response, processing segment data'
              )
              // eslint-disable-next-line
              console.log(
                '[CX DEBUG] Raw segments data:',
                data.response?.segments
              )

              // Process DMP segment data
              const idsObject = data.response.segments.map((d) => ({
                id: d.id,
                shortId: d.shortId
              }))
              // eslint-disable-next-line
              console.log('[CX DEBUG] Processed IDs object:', idsObject)

              // fetch api
              // eslint-disable-next-line
              console.log(
                '[CX DEBUG] Making request to /api/resolve-dmp-user-segments...'
              )
              const cxTargetingResponse = await fetch(
                '/api/resolve-dmp-user-segments',
                {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify(idsObject)
                }
              )
              // eslint-disable-next-line
              console.log(
                '[CX DEBUG] DMP API response status:',
                cxTargetingResponse.status
              )
              // eslint-disable-next-line
              console.log(
                '[CX DEBUG] DMP API response ok:',
                cxTargetingResponse.ok
              )

              const cxTargetingJSON = await cxTargetingResponse.json()
              // eslint-disable-next-line
              console.log('[CX DEBUG] DMP API response JSON:', cxTargetingJSON)

              if (cxTargetingJSON.error) {
                // eslint-disable-next-line
                console.log(
                  '[CX DEBUG] Error in CX targeting response:',
                  cxTargetingJSON.error
                )
                // eslint-disable-next-line
                console.log(
                  '[CX DEBUG] Falling back to base targeting due to error'
                )
                return this.DFPManagerLoadAds(targeting)
              } else {
                const cxTargeting = cxTargetingJSON.results
                // eslint-disable-next-line
                console.log('[CX DEBUG] CX targeting results:', cxTargeting)

                // Get registered DFP slots for debugging if needed
                // DFPManager.getRegisteredSlots()

                // Apply CX targeting parameters
                const targetingWithCx = {
                  ...targeting,
                  ...cxTargeting
                }
                // eslint-disable-next-line
                console.log(
                  '[CX DEBUG] Final targeting with CX segments:',
                  targetingWithCx
                )

                // Apply combined targeting parameters
                // eslint-disable-next-line
                console.log('[CX DEBUG] Loading ads with CX targeting')
                return this.DFPManagerLoadAds(targetingWithCx)
              }
            } else {
              // eslint-disable-next-line
              console.log(
                '[CX DEBUG] Failed DMP response - HTTP status:',
                data.httpStatus
              )
              // eslint-disable-next-line
              console.log('[CX DEBUG] Full error response:', data)
              // eslint-disable-next-line
              console.log('[CX DEBUG] Falling back to base targeting')
              // Handle failed DMP response
              return this.DFPManagerLoadAds(targeting)
            }
          })

          // Okay steps forward now.
          // 1. Add instance.siteGroupId in CMS - EASY
          // 2. Add Sync Piano DMP Segments { rawSegmentData: { ... }, segmentGroups: [] }
          // 3. Create a getPianoDmpSegmentData graphq query { siteGroups: [ { instanceId, siteGroupId } ], segmentGroups: ['group1', ..., 'groupN'] }
          // 4. Use the segmentGroups as keys for userSegments
          // eslint-disable-next-line
          console.log('[CX DEBUG] CX invoke callback completed')
        }
      ])
      // eslint-disable-next-line
      console.log(
        '[CX DEBUG] CX callQueue push completed, current length:',
        cX.callQueue.length
      )
    } else {
      // eslint-disable-next-line
      console.log(
        '[CX DEBUG] Window or user conditions not met, using base targeting'
      )
      // eslint-disable-next-line
      console.log(
        '[CX DEBUG] - Window available:',
        typeof window !== 'undefined'
      )
      // eslint-disable-next-line
      console.log(
        '[CX DEBUG] - User available:',
        !!this.props.pageProps?.pageData?.user
      )
      // eslint-disable-next-line
      console.log('[CX DEBUG] Loading ads with base targeting only')
      return this.DFPManagerLoadAds(targeting)
    }
    // eslint-disable-next-line
    console.log('[CX DEBUG] initialloadAdsWithCxUserSegments completed')
  }

  DFPManagerLoadAds(targeting) {
    // eslint-disable-next-line
    console.log(
      '[CX DEBUG] DFPManagerLoadAds called with targeting:',
      targeting
    )
    setTimeout(() => {
      // eslint-disable-next-line
      console.log('[CX DEBUG] Setting targeting arguments in DFPManager')
      DFPManager.setTargetingArguments(targeting)
      // eslint-disable-next-line
      console.log('[CX DEBUG] Getting Google Tag...')
      DFPManager.getGoogletag().then(function (gt) {
        // eslint-disable-next-line
        console.log('[CX DEBUG] Google Tag obtained, pushing refresh command')
        gt.cmd.push(function () {
          // Log targeting keys before refreshing slots
          const targetingKeys = gt.pubads().getTargetingKeys()
          // eslint-disable-next-line
          console.log(
            '[CX DEBUG] Current targeting keys before refresh:',
            targetingKeys
          )
          // eslint-disable-next-line
          console.log('[CX DEBUG] Refreshing ad slots...')
          gt.pubads().refresh()
          // eslint-disable-next-line
          console.log('[CX DEBUG] Ad slots refresh completed')
        })
      })
    }, 0)
  }

  getNames(arr, key) {
    return arr?.map((item) => item[key])
  }

  // Reset the ad refresh timer (resets the 10-minute countdown).
  resetAdRefreshTimer = () => {
    this.DISABLE_REFRESH_TIMER = 600000 // 10 minutes
    if (this.adRefreshGlobalTimeout) {
      clearTimeout(this.adRefreshGlobalTimeout)
    }
    // Re-enable ad refresh logic for the new page.
    this.adRefreshDisabled = false
    this.adRefreshGlobalTimeout = setTimeout(() => {
      this.disableAdRefreshLogic()
    }, this.DISABLE_REFRESH_TIMER) // 10 minutes
    // console.log(
    //   'Ad refresh timer reset. Ads will refresh for another 10 minutes.'
    // )
  }

  // Disable ad refresh: clear all timers and prevent new ones from being set.
  disableAdRefreshLogic = () => {
    this.adRefreshDisabled = true
    // console.log('Disabling ad refresh logic after 10 minutes.')
    Object.keys(this.adRefreshIntervalMap).forEach((slotId) => {
      const timerData = this.adRefreshIntervalMap[slotId]
      if (timerData) {
        if (timerData.intervalId) {
          clearInterval(timerData.intervalId)
          timerData.intervalId = null
        }
        if (timerData.timeoutId) {
          clearTimeout(timerData.timeoutId)
          timerData.timeoutId = null
        }
      }
    })
  }

  // Call this method from componentDidMount
  handleAutomaticAdUnitRefresh = () => {
    // console.log('handleAutomaticAdUnitRefresh: ', this.adRefreshDisabled)
    if (typeof window === 'undefined') return

    // Object to store timer data for each slot by its id.
    this.adRefreshIntervalMap = {}
    this.REFRESH_INTERVAL = 60000 // 60 seconds

    DFPManager.getGoogletag().then((gt) => {
      gt.cmd.push(() => {
        this.impressionListener = (event) => {
          // If ad refreshing is disabled, do nothing.
          if (this.adRefreshDisabled) {
            return
          }
          const slot = event.slot
          const slotId = slot.getSlotId().getId()

          // console.log('this.adRefreshIntervalMap', this.adRefreshIntervalMap)
          // console.log('IMPRESSION VIEWABLE: slotId', slotId)

          // Only set up a refresh timer if one doesn't exist for this slot.
          if (!this.adRefreshIntervalMap[slotId]) {
            this.adRefreshIntervalMap[slotId] = {
              slot, // Store the slot so we can refresh it later.
              delay: this.REFRESH_INTERVAL,
              startTime: Date.now(),
              intervalId: setInterval(() => {
                gt.pubads().refresh([slot])
                // console.log('refresh', slotId)
                this.adRefreshIntervalMap[slotId].startTime = Date.now()
              }, this.REFRESH_INTERVAL)
            }
          }
        }

        // Listen for when an ad becomes viewable.
        gt.pubads().addEventListener(
          'impressionViewable',
          this.impressionListener
        )
      })
    })

    // Listen for visibility changes to pause/resume refresh timers.
    document.addEventListener('visibilitychange', this.handleVisibilityChange)

    // Start the 10-minute timer on initial load.
    this.resetAdRefreshTimer()
  }

  // This method handles pausing and resuming the timers.
  handleVisibilityChange = () => {
    if (document.hidden) {
      // When the tab is hidden, pause all timers.
      Object.keys(this.adRefreshIntervalMap).forEach((slotId) => {
        const timerData = this.adRefreshIntervalMap[slotId]
        if (timerData && timerData.intervalId) {
          clearInterval(timerData.intervalId)
          timerData.intervalId = null
          // Calculate elapsed time since the last refresh.
          const elapsed = Date.now() - timerData.startTime
          // Save remaining time (or 0 if already overdue).
          timerData.remaining = Math.max(timerData.delay - elapsed, 0)
          // console.log(
          //   `Paused timer for slot ${slotId}, remaining: ${timerData.remaining}ms`
          // )
        }
      })
    } else {
      // When the tab becomes visible again, resume each timer.
      Object.keys(this.adRefreshIntervalMap).forEach((slotId) => {
        const timerData = this.adRefreshIntervalMap[slotId]
        if (timerData && timerData.remaining !== undefined) {
          // console.log(
          //   `Resuming timer for slot ${slotId} with remaining: ${timerData.remaining}ms`
          // )
          // Wait for the remaining time before triggering the refresh.
          timerData.timeoutId = setTimeout(() => {
            DFPManager.getGoogletag().then((gt) => {
              gt.cmd.push(() => {
                gt.pubads().refresh([timerData.slot])
                // console.log('refresh (on resume)', slotId)
              })
            })
            // Reset the timer start time.
            timerData.startTime = Date.now()
            // Restart the interval timer.
            timerData.intervalId = setInterval(() => {
              DFPManager.getGoogletag().then((gt) => {
                gt.cmd.push(() => {
                  gt.pubads().refresh([timerData.slot])
                  // console.log('refresh', slotId)
                })
              })
              timerData.startTime = Date.now()
            }, timerData.delay)
            // Clean up the temporary timeout and remaining time.
            clearTimeout(timerData.timeoutId)
            timerData.timeoutId = null
            timerData.remaining = undefined
          }, timerData.remaining)
        }
      })
    }
  }

  componentDidMount() {
    // Handle piano ad feature flag
    if (this.props.pageProps && this.props.pageProps.instance) {
      const featureFlagHelper = useFeatureFlagHelper(
        this.props.pageProps.instance
      )
      this.isPianoAdsEnabled = featureFlagHelper.isFeatureEnabled('pianoAds')
    }

    //  FEATURE - PIANO ADS
    if (this.isPianoAdsEnabled) {
      // Initialize component and load ads
      this.initialloadAdsWithCxUserSegments()
    }

    if (
      typeof window !== 'undefined' &&
      this.props.pageProps &&
      this.props.pageProps.pageData &&
      this.props.pageProps.pageData.articleIds
    ) {
      window.__CLIENT_DEDUPE = createDeduper()
      this.props.pageProps.pageData.articleIds.map(window.__CLIENT_DEDUPE)
    }

    this.handleAutomaticAdUnitRefresh()

    if (
      this.props.pageProps &&
      this.props.pageProps.instance &&
      this.props.pageProps.instance.googleTagManagerId
    ) {
      TagManager.initialize({
        gtmId: this.props.pageProps.instance.googleTagManagerId
      })
    }
    if (process.env.NODE_ENV !== 'development') {
      Sentry.init({
        dsn: createConfig(this.props.pageProps.instance).sentryDsn,
        environment: process.env.SENTRY_ENV,
        release: packageJson.version,
        ignoreErrors: [
          // Microsoft SafeLink Crawler https://forum.sentry.io/t/unhandledrejection-non-error-promise-rejection-captured-with-value/14062
          'Object Not Found Matching Id:',
          // Unhandled rejections with non-error values - these provide zero information
          'Non-Error promise rejection captured with keys:'
        ]
      })
      Sentry.configureScope((scope) => {
        scope.setTag('application', 'browser')
      })
    } else {
      window.Sentry = {
        // eslint-disable-next-line
        captureException: (...args) => console.error(...args)
      }
    }

    const refreshDataLayer = () => {
      window.dataLayer = window.dataLayer || []
      if (this.props.pageProps.instance) {
        const { instance } = this.props.pageProps
        const { primaryColor, secondaryColor } = brandToColor(instance.theme)
        window.dataLayer.site = {
          name: instance.name,
          strapline: instance.strapline,
          darkLogoUrl: instance.darkLogoUrl,
          lightLogoUrl: instance.lightLogoUrl,
          colors: {
            primary: instance.primaryColorOverride || primaryColor,
            secondary: instance.secondaryColorOverride || secondaryColor
          },
          pianoAid: instance.pianoApplicationId
        }
      }
      if (this.props.pageProps.article) {
        const { article } = this.props.pageProps
        const tagstringsArray = article.tags.map((tagObj) => tagObj.tag)
        window.dataLayer.article = {
          canonical: article.canonicalUrl,
          title: article.headline.replace(/"/g, '\\"'),
          author: article.author?.name || '',
          category: article.category,
          tags: tagstringsArray,
          published: article.displayDate,
          contentType: article.contentType,
          featured: article.featured
        }
      } else {
        window.dataLayer.article = {}
      }
      // Push an 'article_refreshed' event to the DataLayer so GTM knows it has been updated
      window.dataLayer.push({
        event: 'article_refreshed'
      })
    }

    const refreshAds = () => {
      const { router } = this.props

      const {
        graphqlManager,
        google_force_console, // eslint-disable-line camelcase
        ...queryParams
      } = router.query

      const {
        section,
        article,
        magazineIssue,
        resource,
        instance,
        context
      } = this.props.pageProps

      const targeting = this.getTargeting(
        section,
        article,
        magazineIssue,
        resource,
        instance,
        queryParams,
        router
      )

      if ((!instance.preventAdsOnMobile && context?.isDesktop) || !context) {
        DFPManager.setTargetingArguments(targeting)
        if (this.isPianoAdsEnabled) {
          DFPManager.getGoogletag().then(function (gt) {
            gt.cmd.push(function () {
              gt.pubads().refresh()
            })
          })
        } else {
          DFPManager.refresh()
        }
      }
    }

    const refreshPage = () => {
      // Only gets called when the page changes
      refreshDataLayer()
      refreshAds()
      this.cleanup()
      this.resetAdRefreshTimer()
      setTimeout(() => {
        this.handleAutomaticAdUnitRefresh()
        DFPManager.getGoogletag().then((gt) => {
          gt.cmd.push(() => {
            gt.pubads().refresh()
            // console.log('Forced refresh on all ad slots after reinit')
          })
        })
      }, 0)
    }

    // Bug 17 fix: DataLayer not being populated on entry to page or when page is refreshed
    // Every page should have .site on the dataLayer object.
    // Check to see if a DataLayer has been amended by the CMS with .site
    // If so, leave it be.
    // If not, refresh it.

    if (typeof window.dataLayer.site === 'undefined') {
      refreshDataLayer()
    } else {
      // console.log('CMS: ✅ app.jsx has already populated dataLayer')
    }

    this.props.router.events.on('routeChangeComplete', refreshPage)
    return () => {
      this.props.router.events.off('routeChangeComplete', refreshPage)
    }
  }

  cleanup() {
    document.removeEventListener(
      'visibilitychange',
      this.handleVisibilityChange
    )
    if (this.adRefreshGlobalTimeout) {
      clearTimeout(this.adRefreshGlobalTimeout)
    }
    Object.keys(this.adRefreshIntervalMap).forEach((slotId) => {
      const timerData = this.adRefreshIntervalMap[slotId]
      if (timerData.intervalId) {
        clearInterval(timerData.intervalId)
      }
      if (timerData.timeoutId) {
        clearTimeout(timerData.timeoutId)
      }
    })
    DFPManager.getGoogletag().then((gt) => {
      gt.pubads().removeEventListener(
        'impressionViewable',
        this.impressionListener
      )
    })
  }

  componentWillUnmount() {
    this.cleanup()
  }

  render() {
    const { Component, pageProps, router } = this.props
    const {
      instance,
      searchTerm,
      section,
      latestMagazineIssue,
      latestArticles,
      enabledInstances,
      resource,
      // Used in getTargeting and structuredData
      article,
      magazineIssue,
      // Used in getTargeting and structuredData

      eventUmbrella,
      context
    } = pageProps
    const { theme, brandType, headerType, cookieConsentId } = instance
    const themeContextConfig = {
      theme,
      brandType,
      headerType
    }
    const { color, primaryColor, secondaryColor } = brandToColor(theme)
    const config = createConfig(instance)
    const __url = createUrlInstancifier(instance)
    const structuredData = {
      '@context': 'http://schema.org',
      '@type': 'Organization',
      url: instance.subdomain,
      logo: `/meta/${instance.theme}/icon-192.png`,
      image: `/meta/${instance.theme}/icon-192.png`
    }
    const isProduction = process?.env?.NODE_ENV === 'production'
    const cookieProId =
      cookieConsentId && `${cookieConsentId}${!isProduction ? '-test' : ''}`

    // Extract query parameters but not using them in this context
    // We're only using router.query directly where needed

    // const targeting = this.getTargeting(
    //   section,
    //   article,
    //   magazineIssue,
    //   resource,
    //   instance,
    //   queryParams,
    //   router
    // )

    const {
      graphqlManager,
      google_force_console, // eslint-disable-line camelcase
      ...queryParams
    } = router.query

    const targeting = this.getTargeting(
      section,
      article,
      magazineIssue,
      resource,
      instance,
      queryParams,
      router
    )

    const eventProps = {
      eventUmbrella,
      baseUrls: {
        eventUmbrella: eventUmbrella && `/events/${eventUmbrella.slug}`,
        event:
          eventUmbrella &&
          eventUmbrella.event &&
          `/events/${eventUmbrella.slug}/${eventUmbrella.event.slug}`
      }
    }
    const disableAds =
      instance.preventAdsOnMobile && !context?.isDesktop && context

    const { advertSiteId } = instance

    const AdProvider = disableAds ? TransparentWrapper : DFPSlotsProvider

    if (pageProps.pageData) {
      pageProps.pageData.disableAds = disableAds
      pageProps.pageData.advertSiteId = advertSiteId
      pageProps.pageData.eventBaseUrls = eventProps.baseUrls
    }

    return (
      <VersionPathContextProvider versionPath={mappedVersion.versionPath}>
        {cookieProId && (
          <Head>
            {/* <!-- CookiePro Cookies Consent Notice start --> */}
            <link rel="preload" href="https://cookie-cdn.cookiepro.com" />
            <script
              defer
              charSet="UTF-8"
              type="text/javascript"
              data-domain-script={cookieProId}
              src={`https://cookie-cdn.cookiepro.com/consent/${cookieProId}/otSDKStub.js`}
            />
            <script
              id="cookie-pro"
              type="text/javascript"
              dangerouslySetInnerHTML={{
                __html: `function OptanonWrapper() { }`
              }}
            />
            {/* <!-- CookiePro Cookies Consent Notice end --> */}
          </Head>
        )}
        <ThemeContextProvider theme={themeContextConfig}>
          <Head>
            <script
              key="structured-data"
              type="application/ld+json"
              dangerouslySetInnerHTML={{
                __html: JSON.stringify(structuredData)
              }}
            />
            <script
              type="text/javascript"
              dangerouslySetInnerHTML={{
                __html: ` (function(d,s,e,t){e=d.createElement(s);e.type='text/java'+s;e.async='async';e.src='http'+('https:'===location.protocol?'s://s':'://')+'cdn.cxense.com/cx.js';t=d.getElementsByTagName(s)[0];t.parentNode.insertBefore(e,t);})(document,'script');`
              }}
            />
            {instance.googleOptimizeAsynchronous &&
            instance.googleOptimizeContainerId ? (
              <script
                async
                src={`https://googleoptimize.com/optimize.js?id=${instance.googleOptimizeContainerId}`}
              />
            ) : instance.googleOptimizeContainerId ? (
              <>
                <script
                  src={`https://www.googleoptimize.com/optimize.js?id=${instance.googleOptimizeContainerId}`}
                />
                {/* Anti-Flicker needed when loading synchronously */}
                <style>{`.async-hide {opacity: 0!important}`}</style>
                <script
                  dangerouslySetInnerHTML={{
                    __html: `(function(a,s,y,n,c,h,i,d,e){s.className+=' '+y;h.start=1*new Date;h.end=i=function(){s.className=s.className.replace(RegExp(' ?'+y),'')};(a[n]=a[n]||[]).hide=h;setTimeout(function(){i();h.end=null},c);h.timeout=c;})(window,document.documentElement,'async-hide','dataLayer',4000,{'${this.props.pageProps.instance.googleOptimizeContainerId}':true});`
                  }}
                />
              </>
            ) : null}
          </Head>
          <AdProvider
            dfpNetworkId="80843160"
            collapseEmptyDivs
            lazyLoad={{
              fetchMarginPercent: 500,
              renderMarginPercent: 200,
              mobileScaling: 2.0
            }}
            autoLoading={{ lazyLoad: true }}
            {...(this.isPianoAdsEnabled
              ? { disableInitialLoad: true }
              : { targetingArguments: targeting })}
          >
            <RootColorVariables
              colors={{
                theme: color,
                primary: instance.primaryColorOverride || primaryColor,
                secondary: instance.secondaryColorOverride || secondaryColor,
                event:
                  eventUmbrella && eventUmbrella?.event
                    ? eventUmbrella.event?.brandColor
                    : eventUmbrella?.brandColor
              }}
            />
            <SiteContainer
              currentPath={router.asPath}
              instance={instance}
              article={article}
              latestMagazineIssue={latestMagazineIssue}
              latestArticles={latestArticles}
              enabledInstances={enabledInstances}
              searchTerm={searchTerm}
              section={section}
              disableCommunityPicker
              eventProps={eventProps}
              __url={__url}
            >
              <NextSeo {...config.meta} />
              <Component {...pageProps} __url={__url} />
            </SiteContainer>
          </AdProvider>
        </ThemeContextProvider>
      </VersionPathContextProvider>
    )
  }
}

App.propTypes = {
  Component: func,
  pageProps: object
}

const TransparentWrapper = ({ children }) => children

TransparentWrapper.propTypes = {
  children: node
}

const ErrorWrappedApp = withError((props) => <ErrorPage {...props} />)(App)
export default ErrorWrappedApp

// browser event for changing tab (https://stackoverflow.com/questions/1038643/event-for-when-user-switches-browser-tabs)
//
