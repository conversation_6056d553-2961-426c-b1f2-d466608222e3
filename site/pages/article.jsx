import React, { useEffect, useState } from 'react'
import { bool, func, object } from 'prop-types'
import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import createRenderArticleQuery from '@/query/article/render-article'
import dateFormatter from '@/lib/date-formatter'
import Layout from '@/component/Structure/Layout'
import ArticleMeta from '@/component/Meta/Article'
import useReadingTime from '@/hook/useReadingTime'
import getUserAgent from './lib/get-user-agent'
import appendUser from './lib/append-user'
import dynamic from 'next/dynamic'
import Router from 'next/router'
import { isCrawler } from '@/hook/useIsCrawler'

const InfiniteArticles = dynamic(
  () => import('@/component/Primitive/InfiniteArticles/InfiniteArticles'),
  { ssr: false }
)
const ArticlePage = ({
  __url,
  article,
  section,
  pageData,
  instance,
  latestMagazineIssue,
  isCrawler
}) => {
  const [articleRef, stats] = useReadingTime()
  const [metaTitle, setMetaTitle] = useState(`${article.headline}`)

  useEffect(() => {
    const handleRouteChangeStart = (url) => {
      // Only push to history when navigating away to a different page
      if (window.history.state?.source === 'intersection-observer') {
        // Get current URL before navigation and push it to history
        window.history.pushState(
          { source: 'navigation' },
          '',
          window.location.href
        )
      }
    }

    const handlePopState = () => {
      window.location.reload()
      window.scrollTo = () => {}
    }

    Router.events.on('routeChangeStart', handleRouteChangeStart)
    window.addEventListener('popstate', handlePopState)

    return () => {
      Router.events.off('routeChangeStart', handleRouteChangeStart)
      window.removeEventListener('popstate', handlePopState)
    }
  }, [])

  useEffect(() => {
    const current = articleRef.current
    if (!current) {
      return
    }

    const partialUrl = new URL(article.fullUrlPath, window.location.href)
    if (window.location.hash) partialUrl.hash = window.location.hash
    const queryString = window.location.search
    const searchParams = new URLSearchParams(queryString)
    searchParams.forEach((value, key) => {
      partialUrl.searchParams.set(key, value)
    })
    const url = partialUrl?.toString()

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          window.history.replaceState({}, '', url)

          setMetaTitle(`${article.headline}`)
        }
      },
      {
        root: null,
        rootMargin: '10%',
        threshold: 0.1
      }
    )

    observer.observe(current)

    return () => {
      observer.unobserve(current)
    }
    // eslint-disable-next-line
  }, [articleRef])

  if (!article) {
    return null
  }

  article.readingTime = stats && Math.round(stats.minutes)

  const layout =
    section &&
    section.layouts &&
    section.layouts.article &&
    section.layouts.article.layout

  const images =
    article.images.share_widescreen_1200 &&
    article.images.share_widescreen_1200.length
      ? [
          {
            url: `${article.images.share_widescreen_1200[0].url}.jpg`,
            width: 668,
            height: 504,
            alt: article.headline
          }
        ]
      : article.images.hero_landscape_668 &&
        article.images.hero_landscape_668.length
      ? [
          {
            url: `${article.images.hero_landscape_668[0].url}.jpg`,
            width: 668,
            height: 504,
            alt: article.headline
          }
        ]
      : instance.images.share_landscape_1048 &&
        instance.images.share_landscape_1048.length
      ? [
          {
            url: `${instance.images.share_landscape_1048[0].url}.jpg`,
            width: 668,
            height: 504,
            alt: article.headline
          }
        ]
      : [
          {
            url: instance.darkLogoUrl,
            alt: instance.headline
          }
        ]

  return (
    <>
      <ArticleMeta
        title={metaTitle}
        description={
          article.metaDescription || article.sell || article.headline
        }
        seoTitle={article.shareTitle || article.metaTitle || article.headline}
        seoDescription={
          article.shareDescription || article.metaDescription || article.sell
        }
        subdomain={instance.subdomain}
        canonicalUrl={article.canonicalUrl || pageData.url}
        images={images}
        publishedTime={dateFormatter(article.displayDate, 'ISO')}
        authorName={article.author?.name}
        authorSlug={article.author?.slug}
        tags={article.tags.map((tagObj) => tagObj.tag)}
        companies={article.companies.map((obj) => {
          return { name: obj.name, slug: obj.slug }
        })}
        executives={article.executives.map((obj) => {
          return { name: obj.name, slug: obj.slug }
        })}
        partners={article.partners.map((obj) => {
          return { name: obj.name, slug: obj.slug }
        })}
        publisherName="BizClik Media Ltd."
        contentType={article.contentType}
        video={{
          videoId: article.videoId,
          videoProvider: article.videoProvider
        }}
      />
      <div id={`article_${article.slug}`} ref={articleRef}>
        <Layout
          layout={layout}
          __url={pageData.url}
          article={article}
          section={section}
          pageData={pageData}
          paywalled={article.signUpRequired}
          instance={instance}
          latestMagazineIssue={latestMagazineIssue}
        />
      </div>

      {!isCrawler && (
        <InfiniteArticles
          pageData={pageData}
          instance={instance}
          latestMagazineIssue={latestMagazineIssue}
          article={article}
          setMetaTitle={(title) => setMetaTitle(title)}
        />
      )}
    </>
  )
}

ArticlePage.propTypes = {
  __url: func.isRequired,
  section: object,
  article: object,
  pageData: object,
  instance: object,
  latestMagazineIssue: object,
  isCrawler: bool
}

ArticlePage.getInitialProps = async (context) => {
  const query = createRenderArticleQuery()
  const data = await retrieveAndProcessDataForPage(context, query)
  if (data.error) return data
  const instance = data?.response.instance
  const article = data && data.response && { ...data.response.resource }
  const section = article && article.section
  const url = data && data.vars && data.vars.url
  delete data?.response?.resource?.layouts
  let pageData = { url }
  pageData = await appendUser(pageData, context, instance)
  const { userAgent, isDesktop } = getUserAgent(context)
  const ctx = { isDesktop }
  return {
    ...data.response,
    article,
    section,
    pageData,
    context: ctx,
    isCrawler: isCrawler(userAgent)
  }
}

export default ArticlePage
