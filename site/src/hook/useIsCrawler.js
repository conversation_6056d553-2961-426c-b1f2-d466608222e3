export function isCrawler(userAgent = '') {
  const crawlerPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /crawling/i,
    /slurp/i,
    /baiduspider/i,
    /facebookexternalhit/i,
    /embedly/i,
    /quora link preview/i,
    /outbrain/i,
    /pinterest/i,
    /python-requests/i,
    /okhttp/i,
    /whatsapp/i,
    /telegrambot/i,
    /slackbot/i,
    /discordbot/i,
    /applebot/i,
    /twitterbot/i,
    /googlebot/i,
    /bingbot/i,
    /yandex/i,
    /semrush/i,
    /ahrefsbot/i,
    /mj12bot/i
  ]

  return crawlerPatterns.some((pattern) => pattern.test(userAgent))
}
