import eventWidgetExtraProperties from '../lib/event-widget-extra-properties'
import eventButtonGroupProperties from '../lib/event-button-group-properties'

const createQuery = () => `
  ... on EventAgendaSplitHeroWidget {
    ${eventWidgetExtraProperties}
    title
    subtitle
    description
    buttonGroup {
      ${eventButtonGroupProperties}
    }
    theme
    inverse
    direction
    agendaMode
    agendaItems {
      _id
      name
      description
      startDate
      endDate
      localeSafeStartTime
      localeSafeEndTime
      location {
        name
      }
      sessionType {
        name
        shortName
      }
      speakers {
        name
        companyName
        jobTitle
      }
    }
  }
`

export default createQuery
