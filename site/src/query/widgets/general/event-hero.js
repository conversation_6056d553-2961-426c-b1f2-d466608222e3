import eventWidgetExtraProperties from '../lib/event-widget-extra-properties'
import imageProperties from '../lib/image-properties'
import eventButtonGroupProperties from '../lib/event-button-group-properties'

const sponsorProperties = `
  images {
    logo_1x1_320 {
      ${imageProperties}
    }
    logo_1x1_160 {
      ${imageProperties}
    }
  }
`

const createQuery = () => `
  ... on EventHeroWidget {
    ${eventWidgetExtraProperties}
    id
    showWhenEventIs
    title
    subtitle
    description
    backgroundVideoYoutubeId
    align
    lightLogoUrl
    darkLogoUrl
    sponsor {
      ${sponsorProperties}
    }
    buttonGroup {
      ${eventButtonGroupProperties}
    }
    parallax
    useAsHeading
    hideEventLogo
    theme
    themeColorOverride
    useMaxHeight
    hasBigButton
    images {
      desktop_background_72x17_720 {
        ${imageProperties}
      }
      desktop_background_72x17_1440 {
        ${imageProperties}
      }
      mobile_background_72x17_320 {
        ${imageProperties}
      }
      mobile_background_72x17_640 {
        ${imageProperties}
      }
    }
  }
`

export default createQuery
