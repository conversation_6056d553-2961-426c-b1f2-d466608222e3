import widget from '../../widget/widget'

import imageGridWidget from '../../widgets/general/image-grid'
import inlineImageWidget from '../../widgets/general/inline-image'
import textWidget from '../../widgets/general/text'
import placeholderWidget from '../../widgets/general/placeholder'
import eventStatsWidget from '../../widgets/general/event-stats'
import eventMapWidget from '../../widgets/general/event-map'
import eventDetailsWidget from '../../widgets/general/event-details'
import eventMagazineRowWidget from '../../widgets/general/event-magazine-row'
import eventOnDemandPlayerWidget from '../../widgets/general/event-on-demand-player'
import eventOnLivePlayerWidget from '../../widgets/general/event-live-player'
import eventFormWidget from '../../widgets/general/event-form'
import eventTestimonialGridWidget from '../../widgets/general/event-testimonial-grid'
import eventContentGridWidget from '../../widgets/general/event-content-grid'
import eventContentBlockWidget from '../../widgets/general/event-content-block'
import eventTextBlock from '../../widgets/general/event-text-block'
import eventButtonGroup from '../../widgets/general/event-button-group'
import eventHero from '../../widgets/general/event-hero'
import eventCountdown from '../../widgets/general/event-countdown'
import eventAgendaNavigator from '../../widgets/general/event-agenda-navigator'
import eventTieredSponsor from '../../widgets/general/event-tiered-sponsor'
import eventFeaturedTestimonial from '../../widgets/general/event-featured-testimonial'
import eventArticleSplitHeroWidget from '../../widgets/general/event-article-split-hero'
import eventImageSplitHeroWidget from '../../widgets/general/event-image-split-hero'
import eventVideoSplitHeroWidget from '../../widgets/general/event-video-split-hero'
import eventWOFSplitHeroWidget from '../../widgets/general/event-wof-split-hero'
import eventAgendaSplitHeroWidget from '../../widgets/general/event-agenda-split-hero'
import eventIconSplitHeroWidget from '../../widgets/general/event-icon-split-hero'
import eventMapSplitHeroWidget from '../../widgets/general/event-map-split-hero'
import eventStepsWidget from '../../widgets/general/event-steps'
import headerWidget from '../../widgets/general/header'
import advertWidget from '../../widgets/general/advert'
import aboutLiveEventsWidget from '../../widgets/general/about-live-events'
import articleGrid from '../../widgets/general/article-grid'
import eventArticleGrid from '../../widgets/general/event-article-grid'
import eventArticleGridSnippet from '../../widgets/general/event-article-grid-snippet'
import genericCarousel from '../../widgets/general/generic-carousel'
import htmlWidget from '../../widgets/general/html'
import eventSponsorStrip from '../../widgets/general/event-sponsor-strip'
import eventSponsorLinkedArticles from '@/query/widgets/general/event-sponsor-linked-articles'
import eventGrid from '@/query/widgets/general/event-grid'
import eventLatestCta from '@/query/widgets/general/event-latest-cta'
import magazineLatestCta from '@/query/widgets/general/magazine-latest-cta'
import eventHeaderWidget from '@/query/widgets/general/event-header'
import eventFeatureBoxes from '@/query/widgets/general/event-feature-boxes'
import eventImages from '@/query/widgets/general/event-images'
import eventLogoScroller from '@/query/widgets/general/event-logo-scroller'
import logoGrid from '@/query/widgets/general/logo-grid'

const createEventSectionLayoutWidgets = () => [
  widget(),
  htmlWidget(),
  imageGridWidget(),
  inlineImageWidget(),
  textWidget(),
  placeholderWidget(),
  eventMapWidget(),
  eventStatsWidget(),
  eventDetailsWidget(),
  eventMagazineRowWidget(),
  eventTestimonialGridWidget(),
  eventContentGridWidget(),
  eventContentBlockWidget(),
  eventOnDemandPlayerWidget(),
  eventOnLivePlayerWidget(),
  eventFormWidget(),
  eventTextBlock(),
  eventButtonGroup(),
  eventAgendaNavigator(),
  eventTieredSponsor(),
  eventFeaturedTestimonial(),
  eventHero(),
  eventArticleSplitHeroWidget(),
  eventImageSplitHeroWidget(),
  eventVideoSplitHeroWidget(),
  eventWOFSplitHeroWidget(),
  eventAgendaSplitHeroWidget(),
  eventIconSplitHeroWidget(),
  eventMapSplitHeroWidget(),
  eventStepsWidget(),
  eventCountdown(),
  headerWidget(),
  aboutLiveEventsWidget(),
  articleGrid(),
  eventArticleGrid(),
  eventArticleGridSnippet(),
  advertWidget(),
  genericCarousel(),
  eventSponsorStrip(),
  eventSponsorLinkedArticles(),
  eventGrid(),
  eventLatestCta(),
  magazineLatestCta(),
  logoGrid(),
  eventHeaderWidget(),
  eventFeatureBoxes(),
  eventImages(),
  eventLogoScroller()
]

export default createEventSectionLayoutWidgets
