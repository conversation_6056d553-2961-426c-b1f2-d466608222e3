const genericEventFields = () => `
    lightLogoUrl
    darkLogoUrl
    previewId
    name
    description
    metaTitle
    metaDescription
    shareTitle
    shareDescription
    _fullUrl
    proxyEventHomeUrl
    _registerButtonClassName
    useNewNavigationSidebar
    useNavigationDarkTheme
    navigationCtas {
        key
        theme
        label
        type
        buttonId
        buttonClassName
        eventLayoutKey
        eventLayoutSlug
        baseUrl
        link
        openInNewTab
        labelOverride
        label
        theme
    }
    specialActions {
        register {
            key
            type
            buttonId
            buttonClassName
            eventLayoutKey
            eventLayoutSlug
            baseUrl
            link
            openInNewTab
            labelOverride
        }
        live {
            key
            type
            buttonId
            buttonClassName
            eventLayoutKey
            eventLayoutSlug
            baseUrl
            link
            openInNewTab
            labelOverride
        }
        navigation_cta {
            key
            type
            buttonId
            buttonClassName
            eventLayoutKey
            eventLayoutSlug
            baseUrl
            link
            openInNewTab
            labelOverride
        }
        navigation_logo {
            key
            type
            buttonId
            buttonClassName
            eventLayoutKey
            eventLayoutSlug
            baseUrl
            link
            openInNewTab
            labelOverride
        }
    }
    # Keep these for backward compatibility
    _registerAction {
        key
        type
        buttonId
        buttonClassName
        eventLayoutKey
        eventLayoutSlug
        baseUrl
        link
        openInNewTab
        labelOverride
    }
    _liveAction {
        key
        type
        buttonId
        buttonClassName
        eventLayoutKey
        eventLayoutSlug
        baseUrl
        link
        openInNewTab
        labelOverride
    }
`

export default genericEventFields
