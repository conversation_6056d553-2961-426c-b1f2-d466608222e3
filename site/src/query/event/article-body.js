import widgetArea from '../widget/widget-area'
import widget from '../widget/widget'
import eventTextBlock from '../widgets/general/event-text-block'
import eventButtonGroup from '../widgets/general/event-button-group'
import htmlWidget from '../widgets/general/html'
import inlineImageWidget from '../widgets/general/inline-image'
import inlineVideoWidget from '../widgets/general/inline-video'
import textWidget from '../widgets/general/text'
import paginatedListWidget from '../widgets/general/paginated-list'
import podcastWidget from '../widgets/general/podcast'
import keyFactsWidget from '../widgets/general/key-facts'
import tweetWidget from '../widgets/general/tweet'
import blockquoteWidget from '../widgets/general/blockquote'
import logoGrid from '@/query/widgets/general/logo-grid'
import eventHeaderWidget from '../widgets/general/event-header'
import eventFeatureBoxes from '@/query/widgets/general/event-feature-boxes'
import eventImages from '@/query/widgets/general/event-images'
import eventLogoScroller from '@/query/widgets/general/event-logo-scroller'

const widgets = [
  widget(),
  eventTextBlock(),
  eventButtonGroup(),
  htmlWidget(),
  inlineImageWidget(),
  inlineVideoWidget(),
  textWidget(),
  paginatedListWidget(),
  podcastWidget(),
  keyFactsWidget(),
  tweetWidget(),
  blockquoteWidget(),
  logoGrid(),
  eventHeaderWidget(),
  eventFeatureBoxes(),
  eventImages(),
  eventLogoScroller()
]

const createQuery = () => `
  body {
    ${widgetArea(widgets)}
  }
`
export default createQuery
