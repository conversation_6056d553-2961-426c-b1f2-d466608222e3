import React from 'react'
import { EventHeader } from '@/component/Primitive/Events'
import createButtonGroup from '../lib/button-group-creator'
import { string, object, array } from 'prop-types'

const EventHeaderWidget = ({ buttonGroup, title, pageData }) => {
  return (
    <EventHeader
      title={title}
      buttonGroup={createButtonGroup(pageData, buttonGroup)}
    />
  )
}

EventHeaderWidget.propTypes = {
  title: string,
  pageData: object,
  buttonGroup: array
}

export default EventHeaderWidget
