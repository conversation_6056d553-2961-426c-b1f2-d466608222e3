import React, { useMemo } from 'react'
import { string, object, bool, array } from 'prop-types'
import { EventAgendaSplitHero } from '@/component/Primitive/Events'
import createButtonGroup from '../lib/button-group-creator'
import { DateTime } from 'luxon'

const EventAgendaSplitHeroWidget = ({
  pageData,
  buttonGroup,
  agendaItems,
  eventUmbrella,
  ...other
}) => {
  const timezoneAbbreviation = useMemo(() => {
    const zone = eventUmbrella?.event?.timezone
    const firstEventEndDate = eventUmbrella?.event?.agenda?.[0]?.endDate
    if (zone && firstEventEndDate) {
      return DateTime.fromISO(firstEventEndDate, { zone }).toFormat('ZZZZ')
    }

    return 'unset'
  }, [eventUmbrella])

  return (
    <EventAgendaSplitHero
      {...other}
      buttonGroup={createButtonGroup(pageData, buttonGroup)}
      agendaItems={agendaItems}
      timezoneAbbreviation={timezoneAbbreviation}
    />
  )
}

EventAgendaSplitHeroWidget.propTypes = {
  title: string,
  subtitle: string,
  description: string,
  buttonGroup: array,
  theme: string,
  inverse: bool,
  direction: string,
  agendaItems: array,
  pageData: object,
  eventUmbrella: object
}

export default EventAgendaSplitHeroWidget
