import React from 'react'
import { object, bool } from 'prop-types'
import { EventImages } from '@/component/Primitive/Events'

const EventImagesWidget = ({ images, isCarousel }) => {
  const { landscape, portrait } = images
  const preparedImages = []

  for (const i in landscape) {
    preparedImages.push({
      landscape: landscape[i] || null,
      portrait: portrait[i] || null
    })
  }

  for (const i in landscape) {
    preparedImages.push({
      landscape: landscape[i] || null,
      portrait: portrait[i] || null
    })
  }

  return <EventImages isCarousel={isCarousel} images={preparedImages} />
}

EventImagesWidget.propTypes = {
  isCarousel: bool,
  images: object
}

export default EventImagesWidget
