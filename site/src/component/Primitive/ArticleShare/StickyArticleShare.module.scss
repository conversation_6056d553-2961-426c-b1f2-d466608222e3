.StickyArticleShareColumn {
  width: 0;
}

@media (max-width: 1331px) {
  .StickyArticleShareColumn {
    display: none;
  }
}

@media (max-width: 1567px) {
  .StickyArticleShareStoryCTA {
    display: none;
  }
}

@media (max-width: 1567px) {
  .StickyArticleShareTitle {
    inset: auto auto 8px -6px;
    position: absolute;
    transform: rotate(-90deg);
    transform-origin: left bottom;
  }
}

@media (max-width: 1567px) {
  .StickyArticleShareRow {
    align-items: center;
    flex-direction: column;
    gap: 12px;
  }
}

.StickyArticleShareColumnInner {
  display: grid;
  gap: 24px;
  position: sticky;
  top: 108px;
  transform: translateX(-240px);
  width: 184px;
}

@media (max-width: 1567px) {
  .StickyArticleShareColumnInner {
    transform: translateX(-100px);
    width: 48px;
  }
}

.StickyArticleShare {
  border-radius: 48px;
  padding-block: 6px;

  > div {
    align-items: center;
    gap: 8px;
  }
}

@media (max-width: 1567px) {
  .StickyArticleShare {
    width: 48px;

    > div {
      align-items: center;
      flex-direction: column;
      gap: 12px;
    }
  }
}

.StickyArticleShareTitle {
  font-size: 10px !important;
}

.StickyArticleShareRow {
  align-items: center;
  gap: 7px;
}

.StickyArticleShareItem {
  background-color: var(--color-theme--primary) !important;
  border: 1px solid var(--color-theme--primary) !important;
  border-radius: 40px;
  color: $color-white-primary !important;
  display: grid !important;
  height: 40px;
  place-content: center;
  transition: 250ms ease-in-out;
  width: 40px;

  svg {
    &,
    path {
      fill: currentColor !important;
    }
  }

  &:active,
  &:hover {
    background-color: $color-white-primary !important;
    color: var(--color-theme--primary) !important;
  }

  span {
    display: block;
    height: auto !important;
    width: 20px !important;
  }
}
