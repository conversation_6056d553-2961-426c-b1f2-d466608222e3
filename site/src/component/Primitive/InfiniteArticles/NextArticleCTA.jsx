import React, { useEffect, useState } from 'react'
import styles from './NextArticleCTA.module.scss'
import cx from 'classnames'
import Icon from '@/component/Primitive/Icon'
import { func, object } from 'prop-types'

const NextArticleCTA = ({ nextArticle, currentArticle, loadNextArticle }) => {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const handleScroll = () => {
      window.requestAnimationFrame(() => {
        const currentArticleElement = document.querySelector(
          `#article_${currentArticle.slug}`
        )

        if (!currentArticleElement) {
          return
        }

        const { top, height } = currentArticleElement.getBoundingClientRect()
        const scrollPosition = -top
        const fiftyPercent = height * 0.5
        const ninetyPercent = height * 0.8

        setIsVisible(
          scrollPosition >= fiftyPercent && scrollPosition <= ninetyPercent
        )
      })
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll()

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [currentArticle.slug])

  const thumbnailUrl =
    nextArticle?.images?.thumbnail_landscape_322?.[0]?.url || false

  const handleClick = async () => {
    await loadNextArticle()
    const article = document.querySelector(`#article_${nextArticle.slug}`)
    const current = document.querySelector(`#article_${currentArticle.slug}`)

    let top

    if (article) {
      top = article.getBoundingClientRect().top
    } else {
      top = current.getBoundingClientRect().top + current.offsetHeight
    }

    const y =
      top + window.scrollY - document.querySelector('header').offsetHeight + 82

    window.scrollTo({ top: y, behavior: 'smooth' })
  }

  return (
    <div
      className={cx(
        styles.NextArticleCTA,
        isVisible && nextArticle && styles.NextArticleCTAShow
      )}
    >
      {nextArticle && (
        <div className={cx(styles.NextArticleCTAInner)}>
          <div className="next-article-image">
            {thumbnailUrl && <img src={thumbnailUrl} alt="" />}
          </div>

          <div>
            <button
              type="button"
              onClick={handleClick}
              className={cx(styles.NextArticleCTATitle)}
            >
              <span className={cx(styles.NextArticleCTAHeading)}>Up Next:</span>{' '}
              {nextArticle.headline}
            </button>

            <span className={cx(styles.NextArticleCTAGoTo)}>
              Jump to article{' '}
              <Icon
                className={cx(styles.ArrowIcon)}
                type="arrow-down"
                width={10}
                height={6}
              />
            </span>
          </div>
        </div>
      )}
    </div>
  )
}

NextArticleCTA.propTypes = {
  nextArticle: object,
  currentArticle: object,
  loadNextArticle: func
}

export default NextArticleCTA
