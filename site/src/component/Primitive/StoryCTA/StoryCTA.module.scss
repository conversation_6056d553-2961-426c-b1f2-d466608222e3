.Title {
  color: $color-misc-red;
  font-size: 10px;
  font-weight: 500;
  margin-bottom: spacing(1);
  text-transform: uppercase;

  @include mq($breakpoint-desktop) {
    margin-bottom: 8px;
  }
}

.Row {
  align-items: center;
  display: flex;
  gap: 7px;
}

.IconHolder {
  height: 40px;
  position: relative;
  width: 40px;
}

.Icon {
  background-color: var(--color-theme--secondary);
  border: 1px solid var(--color-theme--secondary) !important;
  border-radius: 50%;
  color: $color-white-primary !important;
  display: grid !important;
  height: 100%;
  place-content: center;
  position: relative;
  transition: 250ms ease-in-out;
  width: 100%;
  z-index: 2;

  .Row:has(a:active, a:hover) & {
    background-color: var(--color-theme--primary) !important;
    border: 1px solid var(--color-theme--primary) !important;
  }
}

.IconHolderPulse {
  animation: pulse 5s infinite;
  border: 1px solid var(--color-theme--primary) !important;
  border-radius: 50%;
  height: 50%;
  inset: 50% auto auto 50%;
  opacity: 0;
  position: absolute;
  transform: translate(-50%, -50%);
  width: 50%;
  z-index: -1;
}

@keyframes pulse {
  0% {
    opacity: 0;
  }
  60% {
    opacity: 1;
    width: 50%;
    height: 50%;
  }
  100% {
    opacity: 0;
    width: 200%;
    height: 200%;
  }
}

.IconHolder,
.ArrowIcon {
  flex-shrink: 0;
}

.ArrowIcon {
  height: 10px !important;
  width: 6px !important;

  svg {
    display: block;
  }
}

.StoryCTALink {
  align-items: center;
  color: inherit;
  cursor: pointer;
  display: flex;
  font-size: 10px;
  gap: 7px;

  &:link,
  &:visited {
    text-decoration: none;
  }
}

.LinkText {
  font-weight: 500;
  line-height: 1.3;
  text-transform: uppercase;
}
