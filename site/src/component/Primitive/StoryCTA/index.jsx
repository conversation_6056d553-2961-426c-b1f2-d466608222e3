import classNames from 'classnames'
import { string } from 'prop-types'
import React from 'react'
import Icon from '../Icon'
import SmartLink from '../SmartLink'
import styles from './StoryCTA.module.scss'

const StoryCTA = ({ title, to, as }) => {
  return <StoryCTAComponent title={title} to={to} as={as} />
}

export default StoryCTA

StoryCTA.propTypes = {
  title: string.isRequired,
  to: string.isRequired,
  as: string.isRequired
}

const StoryCTAComponent = ({ title, to, as }) => {
  return (
    <div>
      <h2 className={classNames(styles.Title)}>Did you know?</h2>

      <div className={styles.Row}>
        <div className={styles.IconHolder}>
          <div className={styles.IconHolderPulse} />
          <div className={styles.Icon}>
            <Icon type="open-book" className={styles.Icon} />
          </div>
        </div>

        <SmartLink
          to={to}
          as={as}
          className={styles.StoryCTALink}
          target="_blank"
        >
          <h2 className={classNames(styles.LinkText)}>{title}</h2>

          <Icon className={styles.ArrowIcon} type="arrow-right" />
        </SmartLink>
      </div>
    </div>
  )
}

StoryCTAComponent.propTypes = {
  title: string.isRequired,
  to: string.isRequired,
  as: string.isRequired
}

export const StoryCTAContainer = () => (
  <div id="storyCTA" className={styles.StoryCTAContainer} />
)
