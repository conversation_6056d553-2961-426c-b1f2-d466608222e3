.Grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fill, minmax(min(240px, 100%), 1fr));
  list-style-type: none;
}

.Item {
  display: grid;
  grid: 1fr / 1fr;

  > * {
    grid-area: 1 / 1 / 2 / 2;
  }
}

.ItemBackgroundHolder {
  position: relative;

  &::before {
    aspect-ratio: 308/140;
    content: '';
    display: block;
  }

  img {
    height: 100%;
    inset: 0;
    object-fit: cover;
    position: absolute;
    width: 100%;
  }
}

.ItemInnerText {
  color: $color-white-primary;
  display: grid;
  font-size: 18px;
  font-weight: 700;
  line-height: 1.2;
  padding: 20px;
  place-content: center;
  position: relative;
  text-align: center;
  z-index: 3;

  a {
    display: block;
    text-decoration: none;

    &:link,
    &:visited {
      color: inherit;
    }

    &::before {
      content: '';
      inset: 0;
      position: absolute;
      z-index: 1;
    }
  }

  &::before {
    background-color: var(--color-theme--event);
    content: '';
    inset: 0;
    mix-blend-mode: multiply;
    opacity: 0.75;
    position: absolute;
    transition: opacity 0.2s ease-in-out;
    z-index: -1;
  }

  &:has(a:active, a:hover) {
    &::before {
      opacity: 1;
    }
  }
}
