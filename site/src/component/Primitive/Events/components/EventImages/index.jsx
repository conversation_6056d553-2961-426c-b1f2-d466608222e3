import { arrayOf, bool, number, shape, string } from 'prop-types'
import styles from './EventImages.module.scss'
import React from 'react'
import cx from 'classnames'
import { Splide, SplideSlide, SplideTrack } from '@splidejs/react-splide'
import Icon from '@/component/Primitive/Icon'

const PROP_TYPES = {
  images: arrayOf(
    shape({
      alt: string,
      caption: string,
      height: number,
      link: string,
      ratio: number,
      url: string,
      width: number
    })
  )
}

const EventImagesCarousel = ({ images }) => {
  return (
    <div className={styles.Carousel}>
      <Splide
        hasTrack={false}
        options={{
          gap: '20px',
          pagination: false,
          perPage: 4,
          breakpoints: {
            640: {
              perPage: 1
            },
            768: {
              perPage: 2
            },
            960: {
              perPage: 3
            }
          }
        }}
      >
        <SplideTrack>
          {images?.map(({ landscape }, index) => {
            return (
              <SplideSlide key={index}>
                <div className={styles.CarouselSlide}>
                  <img
                    src={landscape.url}
                    alt={landscape.alt}
                    width={landscape.width}
                    height={landscape.height}
                  />
                </div>
              </SplideSlide>
            )
          })}
        </SplideTrack>

        <div className={cx('splide__arrows', styles.CarouselButtons)}>
          <button
            className={cx(
              'splide__arrow',
              'splide__arrow--prev',
              styles.CarouselButton
            )}
          >
            <Icon width="7" type="arrow-left" />
          </button>

          <button
            className={cx(
              'splide__arrow',
              'splide__arrow--next',
              styles.CarouselButton
            )}
          >
            <Icon width="7" type="arrow-right" />
          </button>
        </div>
      </Splide>
    </div>
  )
}

EventImagesCarousel.propTypes = PROP_TYPES

const EventImagesGrid = ({ images }) => {
  const sections = []

  for (let i = 0; i < images.length; i += 4) {
    sections.push(images.slice(i, i + 4))
  }

  return (
    <div className={styles.Sections}>
      {sections.map((images, index) => {
        const isEven = index % 2 === 0
        return (
          <div
            key={index}
            className={cx(
              styles.Grid,
              isEven ? styles.GridEven : styles.GridOdd
            )}
          >
            {images.map((image, index) => {
              const orientation =
                (isEven && index === 1) || (!isEven && index === 0)
                  ? 'portrait'
                  : 'landscape'

              return (
                <div
                  key={index}
                  className={cx(styles.ImageHolder, styles[orientation])}
                >
                  <img
                    src={image[orientation].url}
                    alt={image[orientation].alt}
                    width={image[orientation].width}
                    height={image[orientation].height}
                  />
                </div>
              )
            })}
          </div>
        )
      })}
    </div>
  )
}

EventImagesGrid.propTypes = PROP_TYPES

export const EventImages = ({ images, isCarousel }) => {
  if (isCarousel) {
    return <EventImagesCarousel images={images} />
  }

  return <EventImagesGrid images={images} />
}

EventImages.propTypes = {
  isCarousel: bool,
  ...PROP_TYPES
}
