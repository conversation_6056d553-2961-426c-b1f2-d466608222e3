.Sections {
  --gap: 10px;

  display: grid;
  gap: var(--gap);
}

@media (min-width: 640px) {
  .Sections {
    --gap: 20px;
  }
}

.Grid {
  display: grid;
  gap: var(--gap);
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
}

.GridEven {
  > * {
    &:nth-child(1) {
      grid-area: 1 / 1 / 3 / 3;
    }

    &:nth-child(2) {
      grid-area: 1 / 3 / 4 / 4;
    }

    &:nth-child(3) {
      grid-area: 3 / 1 / 4 / 2;
    }

    &:nth-child(4) {
      grid-area: 3 / 2 / 4 / 3;
    }
  }
}

.GridOdd {
  > * {
    &:nth-child(1) {
      grid-area: 1 / 1 / 4 / 2;
    }

    &:nth-child(2) {
      grid-area: 1 / 2 / 3 / 4;
    }

    &:nth-child(3) {
      grid-area: 3 / 2 / 4 / 3;
    }

    &:nth-child(4) {
      grid-area: 3 / 3 / 4 / 4;
    }
  }
}

.ImageHolder {
  img {
    height: 100%;
    object-fit: cover;
    width: 100%;
  }
}

.Carousel {
  position: relative;
}

.CarouselSlide {
  img {
    display: block;
  }
}

.CarouselButtons {
  --button-inset: 10px;
}

@media (min-width: $breakpoint-desktopMedium) {
  .CarouselButtons {
    --button-inset: -44px;
  }
}

.CarouselButton {
  background-color: var(--color-theme--event, var(--color-theme--secondary));
  border-radius: 50%;
  color: $color-white-primary;
  display: grid;
  height: 24px;
  inset: calc(50% - 12px) auto auto var(--button-inset);
  place-items: center;
  position: absolute;
  transition: 250ms ease-in-out;
  width: 24px;
  z-index: 10;

  &[disabled] {
    opacity: 0.25;
    pointer-events: none;
  }

  &:active,
  &:hover {
    transform: scale(1.25);
  }

  &:nth-of-type(2) {
    inset: calc(50% - 12px) var(--button-inset) auto auto;
  }
}
