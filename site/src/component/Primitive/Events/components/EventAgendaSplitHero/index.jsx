import React from 'react'
import { Highlight, ButtonGroup } from '@/component/Primitive/Events'
import styles from './EventAgendaSplitHero.module.scss'
import cx from 'classnames'
import Stack from '@/component/Primitive/Stack'
import Type from '@/component/Primitive/Type'
import Prose from '@/component/Primitive/Prose'
import Container from '@/component/Primitive/Container'
import { arrayOf, shape, string, bool, array, oneOf } from 'prop-types'

export const EventAgendaSplitHero = ({
  title,
  subtitle,
  description,
  theme,
  buttonGroup,
  inverse,
  direction,
  agendaItems,
  timezoneAbbreviation
}) => {
  return (
    <div
      className={cx(
        styles.EventAgendaSplitHero,
        theme === 'dark' && styles.dark,
        theme === 'grey' && styles.grey,
        direction === 'column' && styles.column
      )}
    >
      <Container
        gutter
        size="wide"
        center
        className={cx(
          styles.EventAgendaSplitHeroInner,
          inverse && styles.inverse
        )}
      >
        <div className={styles.ContentWrapper}>
          <div className={styles.Content}>
            <Stack gap="medium">
              <Stack gap="small">
                {title && (
                  <Type
                    as="h1"
                    size={['heading5', 'heading2']}
                    weight="medium"
                    className={styles.Title}
                  >
                    <Highlight classNames={{ strong: styles.Highlight }}>
                      {title}
                    </Highlight>
                  </Type>
                )}
                {subtitle && (
                  <Type
                    as="h2"
                    size={['body2', 'body3']}
                    weight="medium"
                    className={styles.Subtitle}
                  >
                    <Prose
                      className={styles.Subtitle}
                      dangerousHtml={subtitle}
                    />
                  </Type>
                )}
                {description && direction !== 'column' && (
                  <Type size={['body2', 'body2']}>
                    <Prose
                      className={styles.Description}
                      dangerousHtml={description}
                    />
                  </Type>
                )}
              </Stack>
              {direction !== 'column' && !!buttonGroup?.length && (
                <ButtonGroup
                  className={styles.ButtonGroup}
                  group={buttonGroup}
                  theme={theme}
                />
              )}
            </Stack>
          </div>
        </div>
        <div className={styles.AgendaWrapper}>
          <div className={styles.AgendaTable}>
            {agendaItems && agendaItems.length > 0 ? (
              agendaItems.map((item, index) => (
                <AgendaItem
                  key={index}
                  item={item}
                  timezoneAbbreviation={timezoneAbbreviation}
                />
              ))
            ) : (
              <Type as="p" size="body3" className={styles.NoAgendaItems}>
                No agenda items available
              </Type>
            )}
          </div>
          <Type as="p" size="body4" className={styles.AgendaNotice}>
            *Timing subject to change
          </Type>
        </div>
        {direction === 'column' && (
          <Stack gap="small" className={styles.OverflowContent}>
            {description && direction === 'column' && (
              <Type size={['body2', 'body2']}>
                <Prose
                  className={styles.Description}
                  dangerousHtml={description}
                />
              </Type>
            )}
            {direction === 'column' && !!buttonGroup?.length && (
              <ButtonGroup
                group={buttonGroup}
                theme={theme}
                className={styles.ButtonGroup}
              />
            )}
          </Stack>
        )}
      </Container>
    </div>
  )
}

const AgendaItem = ({ item, timezoneAbbreviation }) => {
  const { name, localeSafeStartTime, localeSafeEndTime } = item

  // Format time as HH:MM - HH:MM
  const timeRange = `${localeSafeStartTime} - ${localeSafeEndTime} (${
    timezoneAbbreviation || ''
  })`

  return (
    <div className={styles.AgendaItem}>
      <Type as="p" size="body4" weight="bold" className={styles.Date}>
        {timeRange}
      </Type>
      {/* <Type as="p" size="body3" weight="regular" className={styles.StageName}>
        {location?.name}
      </Type> */}
      <Type as="p" size="body3" weight="regular" className={styles.Name}>
        {name}
      </Type>
      {/* <SpeakersColumn speakers={speakers || []} /> */}
    </div>
  )
}

const SpeakersColumn = ({ speakers }) => {
  if (speakers.length === 0) {
    return (
      <div className={styles.Speaker}>
        <Type as="p" size="body3">
          TBC
        </Type>
      </div>
    )
  } else if (speakers.length === 1) {
    return (
      <div className={styles.Speaker}>
        <Type as="p" size="body3" weight="bold" className={styles.SpeakerName}>
          {speakers[0].name}
        </Type>
        <Type
          as="p"
          size="body3"
          weight="regular"
          className={styles.CompanyName}
        >
          {speakers[0].companyName}
        </Type>
      </div>
    )
  } else {
    return (
      <div className={styles.SpeakersWrapper}>
        {speakers.map((speaker, index) => (
          <div key={index} className={styles.Speaker}>
            <Type
              as="p"
              size="body3"
              weight="bold"
              className={styles.SpeakerName}
            >
              {speaker.name}
            </Type>
            <span className={styles.SpeakerDivider}>{' - '}</span>
            <Type
              as="p"
              size="body3"
              weight="regular"
              className={styles.CompanyName}
            >
              {speaker.companyName}
            </Type>
          </div>
        ))}
      </div>
    )
  }
}

EventAgendaSplitHero.propTypes = {
  timezoneAbbreviation: string,
  title: string,
  subtitle: string,
  description: string,
  theme: string,
  buttonGroup: array,
  inverse: bool,
  direction: oneOf(['row', 'column']),
  agendaItems: arrayOf(
    shape({
      _id: string,
      name: string,
      description: string,
      startDate: string,
      endDate: string,
      location: shape({
        name: string
      }),
      sessionType: shape({
        name: string,
        shortName: string
      }),
      speakers: arrayOf(
        shape({
          name: string,
          companyName: string,
          jobTitle: string
        })
      )
    })
  )
}

AgendaItem.propTypes = {
  timezoneAbbreviation: string,
  item: shape({
    name: string,
    startDate: string,
    endDate: string,
    location: shape({
      name: string
    }),
    sessionType: shape({
      name: string,
      shortName: string
    }),
    speakers: arrayOf(
      shape({
        name: string,
        companyName: string,
        jobTitle: string
      })
    )
  })
}

SpeakersColumn.propTypes = {
  speakers: arrayOf(
    shape({
      name: string,
      companyName: string
    })
  )
}
