.EventAgendaSplitHero {
  overflow: hidden;
  padding-bottom: spacing(6);
  padding-top: spacing(6);
  position: relative;

  &.block,
  &.box {
    @include mq($breakpoint-desktop) {
      padding-bottom: spacing(8);
      padding-top: spacing(8);
    }
  }

  &.column {
    text-align: center;

    .EventAgendaSplitHeroInner {
      @include mq($breakpoint-desktop) {
        flex-direction: column;
        gap: spacing(2);
      }
    }

    .ButtonGroup {
      justify-content: center;
    }
  }

  .EventAgendaSplitHeroInner {
    @include mq($breakpoint-desktop) {
      align-items: center;
      display: flex;
      gap: spacing(8);
    }

    &.inverse {
      .ContentWrapper {
        @include mq($breakpoint-desktop) {
          order: 2;
        }
      }

      .AgendaWrapper {
        @include mq($breakpoint-desktop) {
          order: 1;
        }
      }
    }
  }

  .ContentWrapper,
  .AgendaWrapper {
    @include mq($breakpoint-desktop) {
      flex: 1;
    }
  }

  .Content {
    margin-bottom: spacing(4);
    padding-left: spacing(2);
    padding-right: spacing(2);

    @include mq($breakpoint-desktopMedium) {
      margin-bottom: 0;
      max-width: spacing(80);
      padding-left: spacing(6);
      padding-right: spacing(6);
    }
  }

  .Title {
    font-weight: $font-weight-bold;
  }

  .Subtitle {
    color: var(--color-theme--event);
  }

  .Description {
    p {
      font-size: inherit !important;
    }
  }

  .AgendaTable {
    margin-bottom: spacing(2);
  }

  .AgendaItem {
    align-items: center;
    background-color: rgba($color-black-primary, 0.05);
    display: grid;
    gap: spacing(2);
    grid-template-columns: 1fr;
    margin-bottom: spacing(1);
    padding: spacing(2);
    position: relative;

    &:nth-child(even) {
      background-color: rgba($color-black-primary, 0.15);
    }

    @include mq($breakpoint-tablet) {
      gap: 20px;
      grid-template-columns: max-content 4fr;
    }
  }

  .Date {
    font-weight: $font-weight-bold;
  }

  .StageName {
    color: var(--color-theme--event);
    text-align: left;
    text-transform: uppercase;

    @include mq($breakpoint-tablet) {
      padding-left: spacing(1);
    }
  }

  .Name {
    padding-right: spacing(1);
  }

  .SessionType {
    color: var(--color-theme--event);
    text-align: center;
    text-transform: uppercase;
  }

  .SpeakersWrapper {
    display: flex;
    flex-direction: column;

    .Speaker {
      @include mq($breakpoint-desktop) {
        align-items: center;
        display: flex;
        gap: spacing(0.5);
      }
    }

    .SpeakerDivider {
      display: none;

      @include mq($breakpoint-desktop) {
        display: block;
      }
    }
  }

  .Speaker {
    display: flex;
    flex-direction: column;

    @include mq($breakpoint-desktop) {
      align-items: center;
      flex-direction: row;
      gap: spacing(0.5);
    }
  }

  .SpeakerName {
    font-weight: $font-weight-bold;
  }

  .CompanyName {
    font-weight: $font-weight-regular;
  }

  .AgendaNotice {
    color: var(--color-theme--event);
    font-style: italic;
    margin-top: spacing(1);
    text-align: center;
  }

  .OverflowContent {
    margin-top: spacing(4);
  }

  &.grey {
    background-color: $color-grey95;
  }

  &.dark {
    @include darkMode;

    background-color: $color-black-brand;

    .AgendaItem {
      background-color: rgba($color-black-primary, 0.8);

      &:nth-child(even) {
        background-color: $color-black-brand;
        border: 1px solid rgba($color-black-secondary, 1);
      }
    }
  }
}

.Highlight {
  color: var(--color-theme--event) !important;
}
