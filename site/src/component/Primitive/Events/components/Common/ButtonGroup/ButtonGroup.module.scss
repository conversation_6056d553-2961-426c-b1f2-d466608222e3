.ButtonGroup {
  align-items: flex-start;
  background-color: inherit;
  display: flex;
  flex-direction: column;
  gap: spacing(1);

  &.center {
    align-items: center;
    justify-content: center;
  }

  @include mq($breakpoint-desktop) {
    flex-direction: row;
  }

  a {
    text-decoration: none;
  }
}

.Button {
  background-color: var(--color-theme--event) !important;
  color: $color-white-primary !important;

  &:hover,
  &:focus,
  &:active {
    //background-color: $color-grey-secondary !important;
    //color: $color-white-primary;
    //box-shadow: none;
  }
}

.Button.primary--black-text {
  background-color: var(--color-theme--event) !important;
  color: $color-black-primary !important;

  * {
    color: $color-black-primary !important;
  }

  &:hover,
  &:focus,
  &:active {
    &::before {
      background-color: rgba($color-black-primary, 0.1);
    }
  }
}

.Button.secondary {
  background: 0 0 !important;
  box-shadow: inset 0 0 0 1px var(--color-theme--event) !important;
  color: $color-black-primary !important;

  &::before {
    background-color: var(--color-theme--event);
  }

  &:hover,
  &:focus,
  &:active {
    color: $color-white-primary !important;
    //box-shadow: none !important;
  }
}

.Button.tertiary {
  background-color: $color-black-primary !important;

  * {
    color: $color-white-primary !important;
  }

  &::before {
    background-color: rgba($color-white-primary, 0.1);
  }

  &.dark {
    background-color: $color-white-primary !important;

    &::before {
      background-color: $color-black-primary;
    }

    * {
      color: $color-black-primary !important;
      transition: color 250ms ease-in-out;
    }

    &:hover,
    &:focus,
    &:active {
      * {
        color: $color-white-primary !important;
      }
    }
  }
}

@media (min-width: $breakpoint-desktop) {
  .HasBigButton {
    align-items: start !important;
    display: grid !important;
    grid-template-columns: repeat(2, max-content);

    > a:first-of-type {
      grid-area: 1 / 1 / 3 / 3;

      > span {
        justify-content: center !important;
      }
    }
  }
}
