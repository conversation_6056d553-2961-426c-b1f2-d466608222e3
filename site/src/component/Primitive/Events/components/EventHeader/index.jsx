import React from 'react'
import styles from './EventHeader.module.scss'
import Type from '@/component/Primitive/Type'
import { array, string } from 'prop-types'
import { ButtonGroup, Highlight } from '@/component/Primitive/Events'

export const EventHeader = ({ title, buttonGroup }) => {
  return (
    <div className={styles.Row}>
      {title && (
        <Type
          as="h2"
          size={['heading5', 'heading2']}
          weight="regular"
          className={styles.Title}
        >
          <Highlight>{title}</Highlight>
        </Type>
      )}

      {!!buttonGroup?.length && <ButtonGroup group={buttonGroup} />}
    </div>
  )
}

EventHeader.propTypes = {
  title: string,
  buttonGroup: array
}
