import React from 'react'
import { string, array, bool, oneOf, shape, object } from 'prop-types'
import { Highlight, ButtonGroup } from '@/component/Primitive/Events'
import styles from './EventHero.module.scss'
import cx from 'classnames'
import Stack from '@/component/Primitive/Stack'
import Type from '@/component/Primitive/Type'
import Container from '@/component/Primitive/Container'
import YouTubeEmbed from '@/component/Primitive/YouTubeEmbed'

export const EventHero = ({
  title,
  subtitle,
  description,
  backgroundVideoYoutubeId,
  align,
  backgroundImage,
  theme,
  buttonGroup,
  parallax,
  useAsHeading,
  hideEventLogo,
  logos,
  sponsorLogo,
  themeColorOverride,
  useMaxHeight,
  hasBigButton
}) => {
  const { light, dark } = logos || {}
  const { mobile: mobileLogo } = sponsorLogo || {}
  if (sponsorLogo) align = 'left'

  return (
    <div
      className={cx(
        styles.EventHero,
        theme === 'dark' && styles.dark,
        theme === 'grey' && styles.grey,
        useAsHeading && styles.useAsHeading,
        hideEventLogo && styles.hideEventLogo,
        align === 'left' && styles.alignLeft,
        mobileLogo && styles.sponsor,
        useMaxHeight && styles.useMaxHeight
      )}
      style={
        themeColorOverride ? { '--color-theme--event': themeColorOverride } : {}
      }
    >
      {backgroundVideoYoutubeId && (
        <div className={cx(styles.YoutubeEmbed)}>
          <YouTubeEmbed
            hideControls
            autoplay
            loop
            videoId={backgroundVideoYoutubeId}
          />
          <div className={styles.YoutubeEmbedOverlay} />
        </div>
      )}
      <Container
        center
        size={align ? 'large' : 'medium'}
        gutter
        className={cx(styles.EventHeroInner)}
      >
        <div className={styles.ContentWrapper}>
          <Stack gap="medium" className={styles.Stack}>
            {!hideEventLogo && (
              <>
                {theme === 'dark' && dark && (
                  <img src={dark} alt="" className={styles.Logo} />
                )}
                {(theme === 'light' || theme === 'grey') && light && (
                  <img src={light} alt="" className={styles.Logo} />
                )}
              </>
            )}
            <Stack gap="tiny">
              <div className={styles.TitleWrapper}>
                {mobileLogo && (
                  <img
                    src={mobileLogo.src}
                    alt="Sponsor Logo"
                    className={styles.SponsorLogo}
                  />
                )}
                <div className={styles.TitleText}>
                  {title && (
                    <Type
                      as="h1"
                      size={['heading5', mobileLogo ? 'heading1' : 'heading2']}
                      weight="regular"
                      className={styles.Title}
                    >
                      <Highlight
                        classNames={{
                          strong: styles.Highlight
                        }}
                      >
                        {title}
                      </Highlight>
                    </Type>
                  )}
                  {subtitle && (
                    <Type
                      as="h2"
                      size={['body2', 'body3']}
                      weight="medium"
                      className={styles.Subtitle}
                    >
                      {subtitle}
                    </Type>
                  )}
                </div>
              </div>
              {description && (
                <Type size={['body2', 'body2']} className={styles.Description}>
                  <Highlight
                    className={styles.HighlightWrapper}
                    classNames={{
                      strong: styles.Highlight
                    }}
                    options={{
                      preventDefaultFontSize: true
                    }}
                  >
                    {description}
                  </Highlight>
                </Type>
              )}
            </Stack>
            {!!buttonGroup?.length && (
              <ButtonGroup
                group={buttonGroup}
                className={styles.ButtonGroup}
                theme={theme}
                center={align === 'center'}
                hasBigButton={hasBigButton}
              />
            )}
          </Stack>
        </div>
      </Container>
      {backgroundVideoYoutubeId ? (
        <div className={styles.EventVideoOverlay} />
      ) : (
        backgroundImage && (
          <div
            className={cx(styles.EventHeroImage, parallax && styles.parallax)}
          >
            {backgroundImage}
          </div>
        )
      )}
    </div>
  )
}

EventHero.propTypes = {
  title: string,
  subtitle: string,
  description: string,
  backgroundVideoYoutubeId: string,
  align: oneOf(['left', 'center']),
  theme: string,
  buttonGroup: array,
  backgroundImage: object,
  themeColorOverride: string,
  useMaxHeight: bool,
  parallax: bool,
  useAsHeading: bool,
  hideEventLogo: bool,
  logos: shape({
    light: string,
    dark: string
  }),
  sponsorLogo: object,
  hasBigButton: bool
}
