.MagazineLatestCTA {
  /* stylelint-disable */
  container-name: magazinelatestctacontainer;
  container-type: inline-size;
  /* stylelint-enable */
  justify-content: center;
  position: relative;
  text-align: center;

  &::after {
    background-color: #f4f4f4;
    content: '';
    height: 80px;
    inset: auto 0 19px;
    position: absolute;
    width: 100%;
  }
}

/* stylelint-disable */
@container magazinelatestctacontainer (width > 768px) {
  .MagazineLatestCTAInner {
    align-items: center;
    display: grid;
    grid-template-columns: 296px 1fr;
    padding: 20px;

    &:after {
      display: none;
    }
  }
}

/* stylelint-enable */

.MagazineLatestCTAImageHolder {
  /* stylelint-disable */
  aspect-ratio: 296/233;
  /* stylelint-enable */

  margin-inline: 20px;
  perspective: 114px;
  position: relative;
  z-index: 2;

  img {
    box-shadow: rgba(0, 0, 0, 0.24) 0 3px 6px, rgba(0, 0, 0, 0.32) 0 3px 6px;
    height: auto;
    position: absolute;
    top: 50%;
    transition: 250ms ease-in-out;
    translate: 0 -50%;
    width: 40%;
  }
}

.MagazineLatestCTALink {
  background-color: var(--color-theme--primary);
  color: $color-white-primary;
  margin-inline: auto;
  margin-top: 16px;
  z-index: 2;

  &::before {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &::after {
    content: '';
    inset: 0;
    position: absolute;
    z-index: 10;
  }
}

.MagazineLatestCTAImageLeft {
  left: 0;
  transform: translateX(0) translateZ(0);
  z-index: 0;
}

.MagazineLatestCTAImageCentre {
  box-shadow: rgba(0, 0, 0, 0.36) 0 3px 6px, rgba(0, 0, 0, 0.5) 0 3px 6px !important;
  left: 50%;
  transform: translateX(-50%);
  width: calc(40% + 28px) !important;
  z-index: 10;
}

.MagazineLatestCTAImageRight {
  left: 100%;
  transform: translateX(-100%) translateZ(0);
  z-index: 0;
}
