import React, { useEffect, useRef, useState } from 'react'
import { array } from 'prop-types'
import styles from './LogoScroller.module.scss'

const LogoScroller = ({ logos }) => {
  const containerRef = useRef(null)
  const innerRef = useRef(null)
  const [inView, setInView] = useState(false)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      { threshold: 0.1 }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    const abortController = new AbortController()
    const handleScroll = () =>
      requestAnimationFrame(() => {
        if (!inView || !innerRef.current) {
          return
        }

        const shouldTransform =
          innerRef.current.scrollWidth > containerRef.current.scrollWidth
        const distanceFromTopOfWindow = innerRef.current.getBoundingClientRect()
          .top
        const scrollAmount = distanceFromTopOfWindow * 0.25
        innerRef.current.style.transform = shouldTransform
          ? `translateX(-${scrollAmount}px)`
          : null
      })

    window.addEventListener('scroll', handleScroll, abortController)

    return () => abortController.abort()
  }, [inView])

  const logosArray = []
  while (logosArray.length < 20) {
    logosArray.push(...logos)
  }

  return (
    <>
      <div
        ref={containerRef}
        style={{
          overflow: 'hidden'
        }}
      >
        <ul className={styles.Logos} ref={innerRef}>
          {logosArray?.map((client, index) => (
            <li key={index}>
              <img
                width={client.width}
                height={client.height}
                src={client.url}
                alt=""
              />
            </li>
          ))}
        </ul>
      </div>
    </>
  )
}

LogoScroller.propTypes = {
  logos: array
}

export default LogoScroller
