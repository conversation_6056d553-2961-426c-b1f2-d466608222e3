.<PERSON><PERSON> {
  align-items: center;
  display: flex;
  gap: 24px;
  justify-content: center;
  list-style-type: none;
  padding-inline: 20px;

  li,
  img {
    height: var(--h, 40px);
    max-height: 100%;
    max-width: unset;
    width: auto;
  }
}

@media (min-width: 640px) {
  .<PERSON><PERSON> {
    --h: 48px;

    gap: 48px;
  }
}

@media (min-width: 992px) {
  .<PERSON><PERSON> {
    --h: 58px;

    gap: 64px;
  }
}

@media (min-width: 1200px) {
  .<PERSON><PERSON> {
    --h: 66px;
  }
}
