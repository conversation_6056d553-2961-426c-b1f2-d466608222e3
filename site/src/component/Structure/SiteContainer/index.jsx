import React, { useState, useEffect } from 'react'
import { node, object, string, array, bool, shape } from 'prop-types'
import { useRouter } from 'next/router'
import useMedia from '@/hook/useMedia'

import Main from '../Main'
import Footer from '../Footer'
import Header from '../Header'
import styles from './SiteContainer.module.scss'
import { MagazineCTAContainer } from '@/component/Primitive/MagazineCTA'
import { UserContextProvider } from '@/component/Context/UserContext'
import { SignUpContextProvider } from '@/component/Context/SignUpContext'
import { EventAgendaContextProvider } from '@/component/Context/EventAgendaContext'
import SignUpModal from '../../Primitive/SignUpModal/NewsletterSignUpModal'
import { EventAgendaModal } from '@/component/Primitive/Events/components/EventAgenda/components'
import Advert from '@/component/Primitive/Advert'
import generateAdUnitPath from '@/component/Primitive/Advert/ad-unit-path-generator'

import formatSocialLinks from './lib/social-link-formatter'
import { StoryCTAContainer } from '@/component/Primitive/StoryCTA'

const SiteContainer = ({
  article,
  children,
  currentPath,
  instance,
  latestMagazineIssue,
  latestArticles,
  enabledInstances,
  disableCommunityPicker,
  section,
  eventProps
}) => {
  const storeNewsletterSubscriptionStatus = () =>
    process.browser &&
    window.localStorage.setItem('isNewsletterSubscriber', true)
  const getNewsletterSubscriptionStatus = () =>
    process.browser && window.localStorage.getItem('isNewsletterSubscriber')

  const [error, setError] = useState(null)
  const [emailAddress, setEmailAddress] = useState(null)
  const [signUpModalOpen, setSignUpModalOpen] = useState(false)
  const [eventAgendaModalOpen, setEventAgendaModalOpen] = useState(true)
  const [eventAgendaModalItem, setEventAgendaModalItem] = useState(null)
  const [userIsSubscribed, setUserIsSubscribed] = useState(
    getNewsletterSubscriptionStatus() || false
  )
  const [existingSubscriber, setExistingSubscriber] = useState(false)
  const handleSubscribeUser = (existingSubscriber) => {
    if (existingSubscriber) setExistingSubscriber(true)
    setUserIsSubscribed(true)
    storeNewsletterSubscriptionStatus(true)
  }

  const handleSignUpModalClose = () => {
    setSignUpModalOpen(false)
    document.body.classList.remove('has-locked-scrolling')
    document.body.style.top = 'unset'
  }

  const socialLinks = formatSocialLinks(instance)
  const isDesktop = useMedia('(min-width: 960px)')
  const router = useRouter()
  useEffect(() => {
    if (
      !window.localStorage.getItem('pageNavigates') &&
      !window.localStorage.getItem('seenPopup')
    ) {
      window.localStorage.setItem('pageNavigates', 1)
    }

    const handleRouteChange = () => {
      let pageNavigates = parseInt(window.localStorage.getItem('pageNavigates'))
      if (
        window.localStorage.getItem('seenPopup') !== true &&
        window.localStorage.getItem('pageNavigates')
      ) {
        window.localStorage.setItem('pageNavigates', ++pageNavigates)
      }
    }

    if (
      window.localStorage.getItem('pageNavigates') === '3' &&
      !userIsSubscribed &&
      isDesktop
    ) {
      setSignUpModalOpen(true)
      window.localStorage.removeItem('pageNavigates')
      window.localStorage.setItem('seenPopup', true)
    }

    router.events.on('routeChangeStart', handleRouteChange)

    return () => {
      router.events.off('routeChangeStart', handleRouteChange)
    }
  }, [router, userIsSubscribed, isDesktop])
  const instanceEnabled = instance?.enabled
  let mainProps = {}
  if (eventProps) {
    if (eventProps?.eventUmbrella?.event) {
      mainProps = {
        config: {
          noSpacingTop:
            eventProps?.eventUmbrella?.event?.layoutProperties?.noSpacingTop,
          noSpacingBottom:
            eventProps?.eventUmbrella?.event?.layoutProperties?.noSpacingBottom
        }
      }
    } else {
      mainProps = {
        config: {
          noSpacingTop:
            eventProps?.eventUmbrella?.layoutProperties?.noSpacingTop,
          noSpacingBottom:
            eventProps?.eventUmbrella?.layoutProperties?.noSpacingBottom
        }
      }
    }
  }

  const initUser = {
    name: '',
    email: '',
    uid: ''
  }
  const [currentUser, setCurrentUser] = useState(initUser)

  const headerAdSlotSize = '728x90'
  const headerAdSlotPath = generateAdUnitPath(
    null,
    headerAdSlotSize,
    null,
    section,
    article,
    instance?.advertSiteId
  )

  return (
    <UserContextProvider
      user={{
        currentUser,
        setCurrentUser
      }}
    >
      <SignUpContextProvider
        data={{
          modalOpen: signUpModalOpen,
          handleModalVisibility: setSignUpModalOpen,
          setEmailAddress,
          emailAddress,
          userIsSubscribed,
          existingSubscriber,
          handleSubscribeUser,
          error,
          setError
        }}
      >
        <EventAgendaContextProvider
          data={{
            handleCloseModal: () => setEventAgendaModalOpen(false),
            handleOpenModal: () => setEventAgendaModalOpen(true),
            eventAgendaModalItem,
            setEventAgendaModalItem
          }}
        >
          <div className={styles.SiteContainer}>
            {instance.headerAdvertVisible && !instance.headerAdvertBelowNav && (
              <div className={styles.LeaderboardAboveHeader}>
                <Advert
                  slotName={headerAdSlotPath}
                  size={headerAdSlotSize}
                  keyValueTargeting={instance.keyValueTargeting}
                  alignment="center"
                  hasBackground={false}
                />
              </div>
            )}
            {instanceEnabled && (
              <Header
                navItems={instance.navItems}
                currentPath={currentPath}
                lightLogoUrl={instance.lightLogoUrl}
                darkLogoUrl={instance.darkLogoUrl}
                enabledInstances={enabledInstances}
                latestArticles={latestArticles}
                socialLinks={socialLinks}
                showTicker={instance.showTicker}
                userNavigationVisible={instance.pianoEnabled}
                eventProps={eventProps}
              />
            )}
            {instance.headerAdvertVisible && instance.headerAdvertBelowNav && (
              <div className={styles.LeaderboardBelowHeader}>
                <Advert
                  slotName={headerAdSlotPath}
                  size={headerAdSlotSize}
                  keyValueTargeting={instance.keyValueTargeting}
                  alignment="center"
                  hasBackground={false}
                />
              </div>
            )}
            <Main {...mainProps} eventProps={eventProps}>
              {children}
            </Main>
            <MagazineCTAContainer />
            <StoryCTAContainer />
            <Footer
              instanceEnabled={instanceEnabled}
              latestMagazineIssue={latestMagazineIssue}
              logoUrl={instance.lightLogoUrl}
              navItems={instance.footerNavItems}
              navInstanceLinks={instance.footerNavInstanceLinks}
              instanceStrapline={instance.strapline}
              socialLinks={socialLinks}
              currentPath={currentPath}
              headerAdvertVisible={instance.headerAdvertVisible}
              headerAdvertSlotName={`${instance.advertSiteId}${instance.headerAdvertSlotName}`}
              keyValueTargeting={instance.keyValueTargeting}
            />
          </div>
          {signUpModalOpen && <SignUpModal onClose={handleSignUpModalClose} />}
          {eventAgendaModalOpen && <EventAgendaModal />}
        </EventAgendaContextProvider>
      </SignUpContextProvider>
    </UserContextProvider>
  )
}

SiteContainer.propTypes = {
  article: object,
  children: node.isRequired,
  currentPath: string,
  instance: shape({
    navItems: array,
    lightLogoUrl: string,
    darkLogoUrl: string,
    headerAdvertVisible: bool,
    advertSiteId: string,
    headerAdvertSlotName: string,
    keyValueTargeting: array,
    footerNavItems: array,
    strapline: string
  }).isRequired,
  latestMagazineIssue: object,
  latestArticles: array,
  enabledInstances: array,
  disableCommunityPicker: bool,
  section: object,
  eventProps: object
}

export default SiteContainer
