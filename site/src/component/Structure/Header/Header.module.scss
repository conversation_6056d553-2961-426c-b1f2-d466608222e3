.HeaderContainer {
  background: var(--color-theme--primary);
  overflow-x: clip;
  text-align: center;
  z-index: 100;

  &.stickyWithAd {
    position: sticky;
    top: -190px;
  }

  &.sticky {
    position: sticky;
    top: calc((48px + 1px + 4px + 1px) * -1);
  }

  //&.sticky.event {
  //  position: sticky;
  //  top: calc((48px + 1px + 4px + 1px + 80px) * -1);
  //}

  &.sticky.disableSticky {
    position: sticky;
    top: 0;
  }

  // Don't understand the purpose of this
  @include mq($max: $breakpoint-desktopNav - 1) {
    height: 40px;
    position: sticky !important;
    top: 0 !important;
  }

  @include mq($breakpoint-desktopNav) {
    background: unset;
  }
}

.Header {
  background: var(--color-theme--primary);
  box-shadow: 0 8px 18px rgba($color-black, 0.35);
}

.Top {
  background-color: var(--color-theme--primary);
  border-bottom: 1px solid $color-misc-divider;
  padding: 0;
  position: relative;
  z-index: 3;

  @include mq($breakpoint-desktopNav) {
    background-color: $color-black-primary;
    border-bottom: unset;
  }
}

.Bottom {
  @include mq($breakpoint-desktopNav) {
    border-top: 4px solid var(--color-theme--secondary);
    transition: height ease-in-out 0.2s;
  }
}

.Inner {
  align-items: center;
  display: flex;
  justify-content: space-between;

  @include mq($max: $breakpoint-desktopNav - 1) {
    margin: 0 spacing(-1) 0 0;
  }
}

.Logo {
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);

  img {
    height: 50px;

    @include mq($max: $breakpoint-desktopNav - 1) {
      height: spacing(3);
    }
  }
}

.HeaderButton {
  color: $color-white-primary;
  transition: 250ms ease-in-out;

  &:hover,
  &:focus {
    color: var(--color-theme--secondary);

    .inverse & {
      color: $color-white-primary;
    }
  }
}

.HeaderNavToggle {
  height: 12px;
  margin-bottom: 8px;
  position: relative;

  span {
    outline-color: transparent !important;
    padding: 0;
    position: relative;
    transition: background-color 0.2s linear;
    vertical-align: middle;
    will-change: background-color;

    &,
    &::before,
    &::after {
      background: currentColor;
      content: '';
      display: block;
      height: 2px;
      width: 18px;
      z-index: 5;
    }

    &::before,
    &::after {
      content: '';
      position: absolute;
      transition: transform 0.2s ease, margin 0.2s ease 0.1s;
    }

    &::before {
      margin-top: -6px;
    }

    &::after {
      margin-top: 6px;
    }
  }
}

.HeaderNavToggleActive {
  span {
    background-color: transparent;

    &::before {
      margin-top: 0;
      transform: rotate(45deg);
    }

    &::after {
      margin-top: 0;
      transform: rotate(-45deg);
    }
  }
}

.SearchToggle {
  color: $color-white-primary;
  display: inline-block;
  min-height: spacing(5.5);
  padding-right: spacing(3);
  position: relative;

  &:hover,
  &:active,
  &.active {
    color: var(--color-theme--secondary);
  }

  .fullWidth & {
    align-self: flex-end;
    order: 2;

    &::before {
      display: none;
    }
  }
}

.SearchIcon {
  margin-left: spacing(1);
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(0, -50%);

  svg {
    fill: $color-white-primary;
  }

  .SearchToggle:hover &,
  .SearchToggle:focus & {
    svg {
      fill: var(--color-theme--secondary);
    }
  }
}

@include mq($breakpoint-desktopNav) {
  .NavigationListInner {
    display: flex;
    flex-wrap: wrap;
    gap: spacing(1) spacing(4.5);
    justify-content: end;
    padding: 12px 0;

    li {
      > a,
      > button {
        padding-inline: 0;
      }
    }
  }
}

@include mq($max: $breakpoint-desktopNav - 1) {
  .HeaderNavigationWrapper {
    clip-path: polygon(0 0, 100% 0, 100% 41px, 0 41px);
    transition: 250ms ease-in-out;
  }

  .HeaderNavigationWrapperActive {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }
}

.UserNav {
  list-style: none;
  margin-left: -36px !important;
  overflow: hidden;
  padding-top: 4px;
  transition: 250ms ease-in-out width;
  width: 0;

  > div {
    margin-inline: 0 !important;
    padding-left: 36px !important;
  }
}

.UserNavShow {
  width: 184px;

  &:has(svg) {
    width: 154px;

    div[class*='dynamicWidth'] {
      min-width: 110px !important;
    }
  }
}
