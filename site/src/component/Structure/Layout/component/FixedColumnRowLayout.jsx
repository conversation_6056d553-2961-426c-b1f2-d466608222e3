import React from 'react'
import { func, object, bool, number } from 'prop-types'

import WidgetArea from '../../WidgetArea'
import Grid from '@/component/Primitive/Grid'
import Column from '../../Column'
import ArticleShare from '@/component/Primitive/ArticleShare'
import shareStyles from '@/component/Primitive/ArticleShare/StickyArticleShare.module.scss'
import StoryCTA from '@/component/Primitive/StoryCTA'

const FixedColumnRowLayout = ({
  row,
  __url,
  article,
  instance,
  section,
  pageData,
  latestMagazineIssue,
  isArticle,
  rowIndex,
  eventUmbrella
}) => {
  return (
    <>
      {isArticle && (
        <div className={shareStyles.StickyArticleShareColumn}>
          <div className={shareStyles.StickyArticleShareColumnInner}>
            {article.issueSlug && (
              <div className={shareStyles.StickyArticleShareStoryCTA}>
                <StoryCTA
                  to={`/magazine${
                    article.pageNumber ? `?page=${article.pageNumber}` : ''
                  }`}
                  as={`/magazine/${article.issueSlug}${
                    article.pageNumber ? `?page=${article.pageNumber}` : ''
                  }`}
                  title="You can read this and more in the magazine"
                />
              </div>
            )}
            <ArticleShare url={__url} sticky />
          </div>
        </div>
      )}

      <Grid gutter="default" flex grow>
        {row.cols.map(
          (column, columnIndex) =>
            column.attributes &&
            !column.attributes.includes('fixed-width') && (
              <Column
                key={columnIndex}
                column={
                  row.cols.length > 2
                    ? { ...column, width: column.width * 1.333333 }
                    : { ...column, width: undefined }
                }
              >
                <WidgetArea
                  widgets={column.widgetArea.widgets}
                  __url={__url}
                  article={article}
                  section={section}
                  instance={instance}
                  pageData={pageData}
                  latestMagazineIssue={latestMagazineIssue}
                  cappedWidth={isArticle && !row.attributes.includes('inverse')}
                  rowIndex={rowIndex}
                  eventUmbrella={eventUmbrella}
                />
              </Column>
            )
        )}
      </Grid>
      {row.cols.map((column, i) => {
        if (column.attributes && column.attributes.includes('fixed-width')) {
          return (
            <Column
              key={i}
              fixedWidth
              showFirst={row.attributes && row.attributes.includes('reversed')}
            >
              <WidgetArea
                widgets={column.widgetArea.widgets}
                __url={__url}
                article={article}
                section={section}
                instance={instance}
                pageData={pageData}
                lastWidgetIsSticky
                latestMagazineIssue={latestMagazineIssue}
              />
            </Column>
          )
        }
      })}
    </>
  )
}

FixedColumnRowLayout.propTypes = {
  row: object,
  __url: func.isRequired,
  article: object,
  section: object,
  instance: object,
  pageData: object,
  latestMagazineIssue: object,
  isArticle: bool,
  rowIndex: number,
  eventUmbrella: object
}

export default FixedColumnRowLayout
