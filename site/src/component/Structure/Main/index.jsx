import React from 'react'
import { node, shape, object } from 'prop-types'
import classNames from 'classnames'

import styles from './Main.module.scss'
import EventSidebar from '@/component/Structure/EventSidebar'

const configFixture = {
  noSpacingTop: false,
  noSpacingBottom: false
}

const Main = ({ children, config = configFixture, eventProps }) => {
  const { noSpacingTop, noSpacingBottom } = config
  const isEventPage =
    !!Object.keys(eventProps?.eventUmbrella || {})?.length &&
    eventProps?.useNewNavigationSidebar

  return (
    <main
      id="content"
      role="main"
      className={classNames(
        styles.Main,
        noSpacingTop && styles.noSpacingTop,
        noSpacingBottom && styles.noSpacingBottom,
        isEventPage && styles.HasSidebar
      )}
    >
      {isEventPage && <EventSidebar {...eventProps} />}

      {children}
    </main>
  )
}

Main.propTypes = {
  eventProps: object,
  children: node.isRequired,
  config: shape({
    noSpacingTop: node.bool,
    noSpacingBottom: node.bool
  })
}

export default Main
