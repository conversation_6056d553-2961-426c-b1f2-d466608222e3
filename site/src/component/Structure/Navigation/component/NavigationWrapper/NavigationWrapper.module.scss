.NavigationWrapper {
  height: 100%;
  position: relative;
  text-align: center;
  z-index: 2;

  @include mq($max: $breakpoint-desktopNav - 1) {
    padding: 0;
    text-align: left;
  }
}

.NavigationList {
  @include mq($max: $breakpoint-desktopNav - 1) {
    height: 75vh;
    overflow: auto;
  }

  @include mq($breakpoint-desktopNav) {
    display: flex;
    flex-direction: column;

    hr {
      background-color: var(--color-theme--secondary);
      border: 0;
      height: 1px;
      opacity: 0;
    }
  }
}

.NavigationWrapperInner {
  @include mq($breakpoint-desktopNav) {
    align-items: center;
    display: flex;
    justify-content: space-between;
    max-width: spacing(174 + 4);
    padding-left: spacing(4);
    padding-right: spacing(4);
  }
}

.Logo {
  display: none;
  @include mq($breakpoint-desktopNav) {
    display: flex;
    transform-origin: left;
    transition: transform ease-in-out 250ms;

    img {
      height: 54px;
    }
  }

  &.small {
    @include mq($breakpoint-desktopNav) {
      transform: translateY(calc(130px * 0.2)) scale(0.5); // 126px is the height of the header (bottom)
    }
  }
}
